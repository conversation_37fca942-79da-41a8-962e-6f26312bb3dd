# Album Backend

## API 设计

### 对象格式

- Profile

    ```json

    ```

- IllustUrls

    ```json
    [
        {
            "origin": "https://foo.com/.../.../xxxx_p0.jpg",
            "large": "https://foo.com/.../.../xxxx_p0.jpg",
            "thumb": "https://foo.com/.../.../xxxx_p0.jpg"
        },
        ...
    ]
    ```

- Illust

    ```json
    {
        "title": "foo",
        "caption": "xxxxxxx",
        "width": 5120,
        "height": 3840,
        "n_page": 3,
        "n_bookmark": 1000,
        "n_view": 5000,
        "created": "2021-01-01 13:21:19",
        "urls": [
            {
                "origin": "https://foo.com/.../.../xxxx_p0.jpg",
                "large": "https://foo.com/.../.../xxxx_p0.jpg",
                "thumb": "https://foo.com/.../.../xxxx_p0.jpg"
            },
            ...
        ]
    }
    ```

- Tag

    ```json
    {
        "id": 927,
        "name": "洛天依",
        "num": 1024,
        "cover": "http://foobar.com/xxx/yyy/zzz.png"
    }
    ```

### 接口详情

1. 推荐接口

    - 方法：GET
    - 路径: /illust/rcmd
    - 参数：

        | field | type | required | description    |
        |-------|------|----------|----------------|
        | p     | int  | No       | 页码, 默认为 1 |

    - 返回：

        | field   | type         | required | description |
        |---------|--------------|----------|-------------|
        | illusts | List[Illust] | Yes      | Illust 列表 |


2. 排行接口

    - 方法：GET
    - 路径：/api/ranking
    - 参数：

        | field | type     | required | description |
        |-------|----------|----------|-------------|
        | date  | ISO-Date | Yes      |             |
        | p     | int      | No       |             |

    - 返回：

        | field   | type         | required | description |
        |---------|--------------|----------|-------------|
        | illusts | List[Illust] | Yes      | Illust 列表 |


3. 标签推荐接口

    - 方法：GET
    - 路径：/api/tag/rcmd
    - 参数：

        | field | type | required | description |
        |-------|------|----------|-------------|
        | p     | int  | No       |             |

    - 返回：

        | field | type      | required | description |
        |-------|-----------|----------|-------------|
        | tags  | List[Tag] | Yes      | Tag 列表    |

4. 查看标签插画接口

    - 方法：GET
    - 路径：/api/tag/illusts
    - 参数：

        | field | type | required | description |
        |-------|------|----------|-------------|
        | tid   | int  | Yes      | 标签 ID     |
        | p     | int  | No       |             |

    - 返回：

        | field   | type         | required | description |
        |---------|--------------|----------|-------------|
        | illusts | List[Illust] | Yes      | Illust 列表 |

5. 搜索标签接口

    - 方法：GET
    - 路径：/api/tag/search
    - 参数：

        | field | type | required | description |
        |-------|------|----------|-------------|
        | word  | str  | Yes      | 标签名称    |
        | p     | int  | No       |             |

    - 返回：

        | field | type      | required | description |
        |-------|-----------|----------|-------------|
        | tags  | List[Tag] | Yes      | Tag 列表    |

6. 画师推荐接口

    - 方法：GET
    - 路径：/api/artist/rcmd
    - 参数：

        | field | type | required | description |
        |-------|------|----------|-------------|
        | p     | int  | No       |             |

    - 返回：

        | field   | type         | required | description |
        |---------|--------------|----------|-------------|
        | artists | List[Artist] | Yes      | Artist 列表 |


7. 画师作品接口

    - 方法：GET
    - 路径：/api/artist/illusts
    - 参数：

        | field | type | required | description |
        |-------|------|----------|-------------|
        | aid   | int  | Yes      | 画师 ID     |

    - 返回：

        | field   | type         | required | description |
        |---------|--------------|----------|-------------|
        | illusts | List[Illust] | Yes      | Illust 列表 |

8. 搜索画师

    - 方法：GET
    - 路径：/api/artist/search
    - 参数：

        | field | type | required | description  |
        |-------|------|----------|--------------|
        | name  | str  | Yes      | 画师名称、ID |

    - 返回：

        | field   | type         | required | description |
        |---------|--------------|----------|-------------|
        | artists | List[Artist] | Yes      | Artist 列表 |


9.  点赞、收藏、关注

    - 方法：GET
    - 路径：/
    - 参数：

        | field | type | required | description |
        |-------|------|----------|-------------|
        |       |      |          |             |

    - 返回：
        | field | type | required | description |
        |-------|------|----------|-------------|
        |       |      |          |             |

10. 七日登录积分：5  10  10  20  20  40  60

    - 方法：GET
    - 路径：/
    - 参数：

        | field | type | required | description |
        |-------|------|----------|-------------|
        |       |      |          |             |

    - 返回：
        | field | type | required | description |
        |-------|------|----------|-------------|
        |       |      |          |             |
