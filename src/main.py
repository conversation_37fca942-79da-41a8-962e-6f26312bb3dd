from logging import config as logging_config

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from tortoise.contrib.fastapi import register_tortoise

import config as cfg
from common.hooks import register_hooks
from urls import URLS, register_urls

# 加载日志配置
logging_config.dictConfig(cfg.LOGGING)

# 初始化 App
app = FastAPI(
    title=cfg.APP_NAME,
    debug=cfg.DEBUG,
    docs_url='/docs' if cfg.DEBUG else None,
    redoc_url='/redoc' if cfg.DEBUG else None,
    openapi_url='/openapi.json' if cfg.DEBUG else None,
    swagger_ui_oauth2_redirect_url='/docs/oauth2-redirect' if cfg.DEBUG else None,
)
app.mount('/static', StaticFiles(directory=cfg.STATIC_DIR), name='static')  # 挂载静态文件目录
register_tortoise(app=app, config=cfg.DATABASE, generate_schemas=False)  # 设置数据库
register_urls(app, URLS)  # 绑定 URL
register_hooks(app)  # 注册钩子处理器
