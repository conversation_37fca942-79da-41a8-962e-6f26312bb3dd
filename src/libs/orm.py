import json
from collections.abc import Sequence
from datetime import datetime
from pathlib import Path
from typing import Any, ClassVar, Self

from tortoise import Tortoise
from tortoise.backends.base.client import BaseDBAsyncClient
from tortoise.expressions import Q
from tortoise.fields import IntField
from tortoise.models import Model as TortoiseModel

from common.utils import ibatch
from config import CACHE_MAX_AGE, DATABASE, TIME_ZONE
from libs.cache import redis

FieldNames = Sequence[str]

EMPTY = object()


class Model(TortoiseModel):
    """Model 基类"""

    id = IntField(primary_key=True)

    class Meta:  # type: ignore
        abstract = True

    def __str__(self) -> str:
        return f'{self.__class__.__name__}({self.id})'

    def __repr__(self) -> str:
        return f'{self.__class__.__name__}(id={self.id})'

    def __format__(self, format_spec: str) -> str:
        return self.__str__().__format__(format_spec)

    @staticmethod
    async def init_db():
        await Tortoise.init(config=DATABASE)

    async def set_cache(self):
        """设置 Model 对象缓存"""
        key = f'{self.__class__.__name__}::{self.id}'
        await redis.set(key, self, CACHE_MAX_AGE)

    async def del_cache(self):
        """删除 Model 对象缓存"""
        key = f'{self.__class__.__name__}::{self.id}'
        return await redis.delete(key)

    @classmethod
    async def rebuild_cache(cls, *primary_keys: int):
        """重建 Model 对象缓存"""
        # 获取所有主键
        if not primary_keys:
            keys = await redis.keys(f'{cls.__name__}::*')
            primary_keys = tuple(int(key.split('::')[-1]) for key in keys)

        if not primary_keys:
            return

        for kilo_primary_key in ibatch(primary_keys, 1000):
            # 批量获取对象并保存
            objs = await cls.filter(id__in=kilo_primary_key)
            kilo_keys = {f'{cls.__name__}::{obj.id}': obj for obj in objs}
            await redis.mset(kilo_keys)
            # 重设过期时间
            pipeline = redis.pipeline()
            for key in kilo_keys:
                pipeline.expire(key, CACHE_MAX_AGE)
            await pipeline.execute()

    @classmethod
    async def clear_cache(cls):
        """清空 Model 对象缓存"""
        return await redis.del_pattern(f'{cls.__name__}::*')

    @classmethod
    def fields(cls):
        """获取 Model 对象的字段"""
        return cls._meta.fields.copy()

    @classmethod
    async def get(cls, *args: Q, using_db: BaseDBAsyncClient | None = None, **kwargs: Any) -> Self:  # type: ignore
        """获取 Model 对象"""
        pk = kwargs.get('pk') or kwargs.get('id')
        if pk is not None:
            key = f'{cls.__name__}::{pk}'
            if obj := await redis.get(key):
                await redis.expire(key, CACHE_MAX_AGE)  # 每次获取对象时重置过期时间
                return obj  # type: ignore

        obj = await super().get(*args, using_db=using_db, **kwargs)

        await cls.set_cache(obj)

        return obj

    async def save(self, *args, **kwargs) -> None:
        """保存 Model 对象"""
        await super().save(*args, **kwargs)
        await self.set_cache()

    async def delete(self, *args) -> None:  # type: ignore
        """删除 Model 对象"""
        await super().delete(*args)
        await self.del_cache()

    def to_dict(
        self,
        only: FieldNames | None = None,
        exclude: FieldNames | None = None,
        extra: FieldNames | None = None,
    ) -> dict[str, Any]:
        keys = set(only or self._meta.fields)  # type: ignore
        if exclude is not None:
            keys -= set(exclude)
        if extra is not None:  # 追加字段, 常为 property 属性
            keys |= set(extra)

        attrs = {key: getattr(self, key) for key in keys}
        return attrs

    @classmethod
    async def import_from_dict(cls, fields: dict[str, Any], *keys):
        """从字典导入数据到数据库，如果已存在则更新"""
        kwargs: dict[str, Any] = {'defaults': fields}
        if not keys:
            keys = ('id',)
        for k in keys:
            kwargs[k] = fields.pop(k)
        return await cls.update_or_create(**kwargs)


class PixivModel(Model):
    """Pixiv Model 基类"""

    _keymap: ClassVar[dict[str, list[str]]] = {}

    class Meta:  # type: ignore
        abstract = True

    @staticmethod
    def load_json(jsonpath: str | Path):
        with open(jsonpath) as fp:
            return json.load(fp)

    @staticmethod
    def get_value(data: dict, keys: list[str]):
        for k in keys:
            if k not in data:
                return EMPTY
            data = data[k]
        return data

    @staticmethod
    def get_file_time(filepath: str | Path) -> datetime:
        fst = Path(filepath).stat()
        earliest_time = min(fst.st_ctime, fst.st_mtime, fst.st_atime)
        return datetime.fromtimestamp(earliest_time, TIME_ZONE)

    @classmethod
    def extract_fields(cls, jsondata: dict[str, Any], keymap=None):
        """从 JSON 文件中提取字段"""
        attrs = {}
        kmp = keymap or cls.Meta._keymap  # type: ignore
        for name, keys in kmp.items():
            val = cls.get_value(jsondata, keys)
            if val != EMPTY:
                if val is None:
                    attrs[name] = cls._meta.fields_map[name].default
                else:
                    attrs[name] = val
        return attrs

    @classmethod
    async def import_from_json(cls, jsonpath: Path, keymap=None, *keys):
        jsondata = cls.load_json(jsonpath)
        jsondata['_json_file_time'] = cls.get_file_time(jsonpath)
        fields = cls.extract_fields(jsondata, keymap)
        return await cls.import_from_dict(fields, *keys)
