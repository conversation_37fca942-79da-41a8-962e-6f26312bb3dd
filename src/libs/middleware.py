from asyncio.coroutines import iscoroutinefunction

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from libs.http import State


class BaseMiddleware(BaseHTTPMiddleware):
    """中间件基类"""

    async def attempt_call(self, method_name, *args, **kwargs) -> Response | None:  # type: ignore
        """尝试调用钩子方法"""
        method = getattr(self, method_name, None)
        if callable(method):
            if iscoroutinefunction(method):
                return await method(*args, **kwargs)
            else:
                return method(*args, **kwargs)  # type: ignore

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """中间件分发"""
        # Init State
        token = State.init()
        State.set(request=request)

        # Process request
        response = await self.attempt_call('process_request', request)

        # Call next Middleware or ViewFunction
        if response is None:
            response = await call_next(request)

        # Process response
        response = await self.attempt_call('process_response', request, response) or response

        # Reset State
        State.reset(token)
        return response
