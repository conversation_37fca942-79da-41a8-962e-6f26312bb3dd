import readline
import sys
import termios
import tty
from typing import Any

# 初始化 readline
readline.set_history_length(1000)
readline.parse_and_bind('tab: complete')


COLORS = {'black': 30, 'red': 31, 'green': 32, 'yellow': 33, 'blue': 34, 'magenta': 35, 'cyan': 36, 'white': 37}
BRIGHT = {'black': 90, 'red': 91, 'green': 92, 'yellow': 93, 'blue': 94, 'magenta': 95, 'cyan': 96, 'white': 97}
EFFECTS = {'reset': 0, 'bold': 1, 'dim': 2, 'italic': 3, 'underline': 4, 'blink': 5, 'reverse': 7, 'hidden': 8}
ERASE_END = '\x1b[K\r'
BREAK_END = '\x1b[K\n'


def color(text: str, color: str, effect: str = 'reset', bright: bool = False) -> str:
    """彩色输出"""
    if color in ['gray', 'grey']:
        color, bright = 'black', True

    color_code = BRIGHT[color] if bright else COLORS[color]
    effect_code = EFFECTS[effect]
    return f'\x1b[{effect_code};{color_code}m{text}\x1b[0m'


__all__ = [
    'blue',
    'color',
    'cyan',
    'fill_str',
    'gray',
    'green',
    'magenta',
    'print_dbg',
    'print_err',
    'print_inf',
    'print_ok',
    'print_warn',
    'read_chars',
    'read_lines',
    'red',
    'white',
    'yellow',
]


def gray(message: Any, bright=False, bold=False) -> str:
    """灰色字体"""
    return color(message, 'gray', bright=bright, effect='bold' if bold else 'reset')


def red(message: Any, bright=False, bold=False) -> str:
    """红色字体"""
    return color(message, 'red', bright=bright, effect='bold' if bold else 'reset')


def green(message: Any, bright=False, bold=False) -> str:
    """绿色字体"""
    return color(message, 'green', bright=bright, effect='bold' if bold else 'reset')


def yellow(message: Any, bright=False, bold=False) -> str:
    """黄色字体"""
    return color(message, 'yellow', bright=bright, effect='bold' if bold else 'reset')


def blue(message: Any, bright=False, bold=False) -> str:
    """蓝色字体"""
    return color(message, 'blue', bright=bright, effect='bold' if bold else 'reset')


def magenta(message: Any, bright=False, bold=False) -> str:
    """品红色字体"""
    return color(message, 'magenta', bright=bright, effect='bold' if bold else 'reset')


def cyan(message: Any, bright=False, bold=False) -> str:
    """青色字体"""
    return color(message, 'cyan', bright=bright, effect='bold' if bold else 'reset')


def white(message: Any, bright=False, bold=False) -> str:
    """白色字体"""
    return color(message, 'white', bright=bright, effect='bold' if bold else 'reset')


def print_dbg(message: Any, flash=True) -> None:
    """打印调试信息"""
    ender = ERASE_END if flash else BREAK_END
    print(gray(message, bright=True), end=ender)


def print_inf(message: Any, flash=False) -> None:
    """打印信息"""
    ender = ERASE_END if flash else BREAK_END
    print(blue(message, bright=True), end=ender)


def print_note(message: Any, flash=False) -> None:
    """打印信息"""
    ender = ERASE_END if flash else BREAK_END
    print(cyan(message, bright=True), end=ender)


def print_ok(message: Any, flash=False) -> None:
    """打印成功信息"""
    ender = ERASE_END if flash else BREAK_END
    print(green(message, bold=True), end=ender)


def print_warn(message: Any, flash=False) -> None:
    """打印警告信息"""
    ender = ERASE_END if flash else BREAK_END
    print(yellow(message, bold=True), end=ender)


def print_err(message: Any, flash=False) -> None:
    """打印错误信息"""
    ender = ERASE_END if flash else BREAK_END
    print(red(message, bold=True), end=ender)


def read_chars(message: str, n: int = 0) -> str:
    """提示用户输入"""
    if n > 0:
        print(blue(message, bright=True, bold=True), end='', flush=True)

        try:
            # 设置终端为 “原始模式” 以读取指定长度的字符，而不需要按回车
            fd = sys.stdin.fileno()
            old_settings = termios.tcgetattr(fd)
            tty.setraw(fd)
            chars = sys.stdin.read(n)
        finally:
            termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)  # 恢复终端设置
            print(chars)
        return chars
    else:
        return input(message)


def read_lines(message: str, eof=''):
    """读取多行输入"""
    print(blue(f'{message}\n', bright=True, bold=True), end='', flush=True)
    lines = []
    count = 1
    while True:
        line = input(blue(f'{count:3d} > '))
        if line == eof:
            break
        lines.append(line)
        count += 1
    return '\n'.join(lines)


def fill_str(string: str, display_length: int, char=' ', adjust='right') -> str:
    """填充空白符，使字符串达到指定的显示长度"""
    # 计算字符串的显示长度及要填充的字符数量
    uni_len = len(string)
    utf_len = len(string.encode('utf-8'))
    s_length = uni_len + (utf_len - uni_len) // 2
    n_fill = display_length - s_length
    if adjust == 'right':
        return string + n_fill * char
    elif adjust == 'left':
        return n_fill * char + string
    elif adjust == 'center':
        half = n_fill // 2
        return half * char + string + (n_fill - half) * char
    else:
        raise ValueError('invalid adjust value')
