"""Cloudflare Turnstile Helper"""

import httpx

from config import server as cfg

CF_VERIFY_URL = 'https://challenges.cloudflare.com/turnstile/v0/siteverify'


class TurnstileTokenError(Exception):
    pass


async def verify(token: str, ip: str | None = None) -> bool:
    """
    Verifies a Cloudflare Turnstile token.
    :param token: The token from the 'cf-turnstile-response' field.
    :param ip: The user's IP address (optional but recommended).
    :return: True if the token is valid, False otherwise.
    """
    if not token:
        raise TurnstileTokenError('请先完成人机验证')

    payload = {
        'secret': cfg.CF_TUR_SECRET_KEY,
        'response': token,
    }
    if ip:
        payload['remoteip'] = ip

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(CF_VERIFY_URL, data=payload)
            response.raise_for_status()
            result = response.json()
        if result.get('success'):
            return True
        return False
    except httpx.RequestError:
        # 网络问题或Cloudflare服务不可用时，选择失败（更安全）
        return False
    except Exception:
        return False
