import os
import re
from functools import cached_property
from os.path import join

from b2sdk import v2

import config as cfg

STATIC_PATTERN = re.compile(r'^(css|js|img)')

__all__ = ['b2', 'get_private_url', 'get_private_urls', 'static']


def static(*paths: str):
    """生成静态文件的 URL"""
    if STATIC_PATTERN.match(paths[0]):
        path = join(cfg.STATIC_URL, *paths)
        # 获取文件的修改时间作为版本号
        file_path = os.path.join(cfg.STATIC_DIR, *paths)
        if os.path.exists(file_path):
            mtime = int(os.path.getmtime(file_path))
            return f'{path}?v={mtime}'
        return path

    elif cfg.STORAGE == 'b2':
        return join(cfg.B2['root'], cfg.B2['public'], *paths)

    elif cfg.STORAGE == 'cloudflare':
        return join(cfg.CF_ROOT, *paths)

    else:
        return join(cfg.STATIC_URL, *paths)


class B2:
    """B2 存储"""

    def __init__(self):
        self.api = v2.B2Api(v2.InMemoryAccountInfo())
        self.authorized = False

    def authorize(self):
        """授权"""
        if not self.authorized:
            self.api.authorize_account('production', cfg.B2['key_id'], cfg.B2['app_key'])
            self.authorized = True

    @cached_property
    def public(self):
        """公共桶"""
        self.authorize()
        return self.api.get_bucket_by_name(cfg.B2['public'])

    @cached_property
    def private(self):
        """私有桶"""
        self.authorize()
        return self.api.get_bucket_by_name(cfg.B2['private'])


b2 = B2()


def get_private_url(ftype: str, fname: str, expiration=600) -> str:
    """从 B2 私有桶获取文件的下载链接"""

    fullname = join(ftype, fname)
    file_url = b2.private.get_download_url(fullname)
    token = b2.private.get_download_authorization(fullname, valid_duration_in_seconds=expiration)
    return f'{file_url}?Authorization={token}'


def get_private_urls(ftype: str, fnames: list[str], expiration=600) -> list[str]:
    """从 B2 私有桶一次获取多个文件的下载链接"""
    token = b2.private.get_download_authorization(ftype, valid_duration_in_seconds=expiration)
    urls = []
    for fname in fnames:
        file_url = b2.private.get_download_url(join(ftype, fname))
        full_url = f'{file_url}?Authorization={token}'
        urls.append(full_url)
    return urls
