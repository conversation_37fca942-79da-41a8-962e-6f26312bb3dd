import datetime
import operator
import time
from collections import UserDict
from typing import Any, Self
from uuid import uuid4

from config import SECRET_KEY, TIME_ZONE
from libs.cache import redis


class Session(UserDict):
    """会话管理器"""

    PREFIX = 'Session::'
    LAST_SID = 'LastSid'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._changed = False
        self._saved = False

    def __setitem__(self, key: str, value: Any) -> None:
        if key == '_id' and not value:
            raise ValueError('Session ID 不能为空')
        if key == 'uid' and not (isinstance(value, int) and value > 0):
            raise ValueError('User ID 必须为非 0 整数')

        super().__setitem__(key, value)
        self._changed = True
        self._saved = False

    def __delitem__(self, key: str) -> None:
        super().__delitem__(key)
        self._changed = True
        self._saved = False

    def clear(self) -> None:
        super().clear()
        self._changed = True
        self._saved = False

    @property
    def id(self) -> str | None:
        return self.get('_id')

    @property
    def cache_key(self) -> str:
        if not self.id:
            raise ValueError('Session ID 不能为空')
        return f'{self.PREFIX}{self.id}'

    @property
    def uid(self):
        return self.get('uid')

    @property
    def s_data(self):
        """返回会话数据"""
        return {k: v for k, v in self.items() if not k.startswith('_')}

    @property
    def is_empty(self) -> bool:
        """检查会话是否为空"""
        return not self.s_data

    @property
    def changed(self) -> bool:
        return self._changed

    @classmethod
    def new_id(cls) -> str:
        """生成新的 Session ID"""
        return bytes(a ^ b for a, b in zip(uuid4().bytes, SECRET_KEY, strict=False)).hex()

    @property
    def need_save(self) -> bool:
        """检查是否需要保存会话"""
        return self.changed and not self.saved

    @property
    def saved(self) -> bool:
        return self._saved

    async def last_sid(self) -> str | None:
        """获取用户上次使用的 Session ID"""
        return await redis.hget(self.LAST_SID, self.uid) if self.uid else None

    async def save(self) -> str | None:
        """保存会话"""
        if self.is_empty:
            await self.delete()
            return None
        if not self.need_save:
            return None

        # 生成 Session ID，并保存到 Redis 中
        self._changed, self._saved = False, True  # 修改状态

        if self.id:
            pipeline = redis.pipeline()
            pipeline.hset(self.cache_key, mapping=self.s_data)  # 保存 Session 数据
            pipeline.hset(self.LAST_SID, self.uid, self.id)
            await pipeline.execute()
            return None
        elif self.uid and (last_sid := await self.last_sid()):
            self['_id'] = last_sid
            await redis.hset(self.cache_key, mapping=self.s_data)  # 先用最新的数据覆盖旧的 Session
            self.update(await redis.hgetall(self.cache_key))  # 再重新加载更新后的全部 Session 数据
            return last_sid
        else:
            self['_id'] = self.new_id()
            pipeline = redis.pipeline()
            pipeline.hset(self.cache_key, mapping=self.s_data)
            pipeline.hset(self.LAST_SID, self.uid, self.id)
            await pipeline.execute()
            return self.id

    @classmethod
    async def load(cls, session_id: str | None) -> Self:
        """加载会话"""
        if session_id:
            cache_key = f'{cls.PREFIX}{session_id}'
            if s_data := await redis.hgetall(cache_key):
                session = cls(s_data)
                session['_id'] = session_id
                return session
        return cls()

    async def delete(self, physical=False) -> None:
        """删除会话"""
        if not physical and self.uid:
            self['last'] = int(time.time())
            await redis.hset(self.cache_key, mapping=self.s_data)
        elif self.id:
            await redis.delete(self.cache_key)

        self.clear()

    def update_last_time(self) -> None:
        """更新最后活跃时间"""
        if self.uid:
            now, last = int(time.time()), self.get('last', 0)
            if now - last >= 60 * 5:
                self['last'] = now

    @classmethod
    async def recent_active(cls, num: int = 0, days: int = 0) -> list[tuple[int, str]]:
        """获取最近的用户"""
        active_users = []

        # 遍历所有 Session
        for key in await redis.keys(f'{cls.PREFIX}*'):
            if session_data := await redis.hgetall(key):
                if (uid := session_data.get('uid')) and (last_time := session_data.get('last')):
                    active_users.append({'uid': int(uid), 'last': int(last_time)})

        # 按最近活跃时间排序（从大到小），并截取前 num 个
        active_users.sort(key=operator.itemgetter('last'), reverse=True)

        result = []
        min_day = datetime.datetime.now(TIME_ZONE).date() - datetime.timedelta(days=days - 1)
        for n, user in enumerate(active_users, start=1):
            if num and n > num:
                break
            if days and datetime.datetime.fromtimestamp(user['last'], TIME_ZONE).date() < min_day:
                break
            last_time_str = time.strftime('%Y-%m-%d %H:%M', time.localtime(user['last']))
            result.append((user['uid'], last_time_str))
        return result
