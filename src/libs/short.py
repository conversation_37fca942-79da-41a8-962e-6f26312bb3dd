'''生成短链接'''

import httpx

from config import SHORT_URL


async def gen_short(url: str):
    '''生成短链接'''
    headers = {
        'Authorization': f'Token {SHORT_URL["key"]}',
        'Content-Type': 'application/json',
        "User-Agent": "Mozilla/5.0 (Android 12; Mobile; rv:122.0) Gecko/122.0 Firefox/122.0",
    }
    data = {'url': url}

    async with httpx.AsyncClient() as client:
        response = await client.post(SHORT_URL['api'], headers=headers, json=data)

    if response.status_code == 200:
        result = response.json()
        if result.get('error') == 0:
            return result['short']
        else:
            raise RuntimeError(result['msg'])
    raise RuntimeError(f'generate short url failed: {response.status_code}')
