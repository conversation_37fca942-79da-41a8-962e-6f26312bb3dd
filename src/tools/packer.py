#!/usr/bin/env python
"""二次元、Cosplay专辑发布工具"""

import argparse
import os
import re
import sys
from collections import Counter, defaultdict
from pathlib import Path

# 添加项目目录到系统路径
PROJECT_DIR = Path(__file__).absolute().parent.parent
sys.path.append(PROJECT_DIR.as_posix())

import config as cfg
from apps.album.meta import AlbumMeta, GalleryMeta
from apps.album.models import Category
from apps.pixiv.models import Tag
from common import categories
from common import utils as ut
from libs.terminal import print_err, print_inf, print_ok, print_warn, read_chars

JSON_DIR = Path(os.environ.get('J', '/Users/<USER>/src/acfans/JSON')) / 'illust'
ACG_ALBUM_MAX_SIZE = ut.GB  # 专辑最大大小: 1GB
ACG_ALBUM_MAX_IMG = 200  # 专辑最大图片数量: 200张
ACG_ALBUM_MIN_IMG = 20  # 专辑最小图片数量: 20张


def relocate_illust(src_ipath: Path, dst_ipath: Path, mode: str = 'link', dry_run: bool = False):
    """将插画移动或链接到目标位置, 跳过同名文件"""
    if src_ipath.absolute() == dst_ipath.absolute():
        return False  # 未执行任何操作，直接返回

    if dst_ipath.exists():
        if src_ipath.samefile(dst_ipath):
            print_inf(f'exists same file: {dst_ipath}, skip', flash=True)
            return True  # 路径不同说明已经操作过
        else:
            print_warn(f'exists another version: {dst_ipath}, skip', flash=True)
            return False  # 跳过同名文件

    # 创建目标父级目录
    if not dst_ipath.parent.exists():
        dst_ipath.parent.mkdir(parents=True, exist_ok=True)

    # 模拟移动或链接
    if dry_run:
        print(f'[{mode}] {src_ipath} -> {dst_ipath}')
        return True

    # 移动或链接插画
    if mode == 'move':
        print(f'move {src_ipath} -> {dst_ipath}')
        src_ipath.rename(dst_ipath)
    else:
        print(f'link {src_ipath} <= {dst_ipath}')
        dst_ipath.hardlink_to(src_ipath)
    return True


def categorize_illusts(src_dirs: list[Path], dst_dir: Path, dry_run: bool = False):
    """对插画进行分类"""
    for ipath, ijson in ut.find_illust_and_json(src_dirs, JSON_DIR):
        tags = ijson['tags']
        # 根据 Tag 匹配一级分类
        if matched_categories := categories.match_major_categories(tags):
            cat_1 = matched_categories[0]
        else:
            cat_1 = '其他'  # 无明显特征的 AI 作品也归为 "其他"

        if categories.has_minor_category(cat_1):
            # 对包含子类的插画匹配二级分类
            cats_2 = categories.match_minor_categories(tags, cat_1)
            match len(cats_2):
                case 0:
                    # 未匹配到子分类的插画，归为 "合集"
                    dst_ipath = dst_dir / cat_1 / '合集' / ipath.name
                    relocate_illust(ipath, dst_ipath, 'move', dry_run)
                case 1:
                    # 匹配到一个子分类的插画，直接移动
                    dst_ipath = dst_dir / cat_1 / cats_2[0] / ipath.name
                    relocate_illust(ipath, dst_ipath, 'move', dry_run)
                case _:
                    # 匹配到多个子分类的插画，创建硬链接
                    relocated = True
                    for cat_2 in cats_2:
                        dst_ipath = dst_dir / cat_1 / cat_2 / ipath.name
                        relocated &= relocate_illust(ipath, dst_ipath, 'link', dry_run)
                    if relocated and not dry_run:
                        ipath.unlink()

        elif cat_1 == '原创':
            # 对原创插画按作者分类
            uid = ijson['user']['id']
            name = ut.clean_name(ijson['user']['name']) or 'Unnamed'
            dst_ipath = dst_dir / cat_1 / f'{uid}_{name}' / ipath.name
            relocate_illust(ipath, dst_ipath, 'move', dry_run)

        else:
            # 不包含子类的插画直接移动
            dst_ipath = dst_dir / cat_1 / ipath.name
            relocate_illust(ipath, dst_ipath, 'move', dry_run)


def auto_discover_categories(
    src_dir: list[Path],
    exclude_sensitive: bool = False,
    exclude_major: bool = False,
    exclude_minor: bool = False,
):
    """根据 Tag 自动发现新分类"""
    counter: Counter[str] = Counter()
    # 从指定目录中获取所有插画，并根据插画 ID 找到对应的 JSON 文件
    for _, ijson in ut.find_illust_and_json(src_dir, JSON_DIR):
        if tags := ijson['tags']:
            names = set()
            for tag in tags:
                name, trans = tag['name'], tag['translated_name'] or ''
                # 过滤掉不需要的 Tag
                if Tag.is_bookmark_tag(name, trans):
                    continue
                # 排除敏感词
                if exclude_sensitive and (ut.is_sensitive(name) or ut.is_sensitive(trans)):
                    continue
                # 排除已经存在的分类
                if exclude_major and categories.match_tag_category(name, trans, categories.MajorCategories):
                    continue
                # 排除已经存在的子分类
                if exclude_minor and categories.match_any_minor_category(name, trans):
                    continue

                rname = ut.replace_cn_punctuation(ut.readable_tag(name, trans))
                names.add(rname)

            counter.update(names)

    # 按照 Tag 的插画数量进行降序排列
    for name, count in counter.most_common():
        if count > 10:
            print(f'{count:>6d}\t{name}')


def rename(target: str, regexp: str | None, pattern: str | None, replace: str, ignore_case: bool = False):
    """批量重命名插画"""
    pat: re.Pattern | str = ''
    if regexp:
        try:
            pat = re.compile(regexp, re.IGNORECASE if ignore_case else 0)
            repl = eval(replace) if replace.startswith('lambda') else replace  # noqa: S307
        except re.error as e:
            print_err(f'Invalid regular expression: {e}')
            sys.exit(1)
    elif pattern:
        pat = pattern
        repl = replace
    else:  # 无匹配模式
        print_err('No pattern specified.')
        sys.exit(1)

    # 进行重命名
    if not ut.batch_rename(target, pat, repl, dry_run=True):
        print_err('No files to rename.')
    elif read_chars('Are you sure to rename these files? [y/n] ', n=1).lower() == 'y':
        ut.batch_rename(target, pat, repl, dry_run=False)  # 执行重命名
        print_ok('Done!')
    else:
        print_err('Canceled!')


def pack_illusts(src_dir: Path, max_count: int = 180, min_count: int = 30):
    """打包插画"""
    # 遍历目标目录
    # 查找仅包含图片（扩展名为 jpg, png, gif），且名字不是数字的目录。
    # 忽略隐藏文件和隐藏目录
    # 打印目录名称和插画数量
    for root, dirs, files in os.walk(src_dir):
        root_path = Path(root)

        # 筛选条件：忽略隐藏目录、数字目录、有子目录的目录
        if root_path.name.startswith('.') or root_path.name.isdigit() or dirs:
            if root_path.name.startswith('.'):
                dirs.clear()  # 不再遍历隐藏目录下的子目录
            continue

        # 过滤隐藏文件并检查是否全部为插画
        valid_files = [f for f in files if not f.startswith('.')]
        illust_files = [f for f in valid_files if Path(root, f).suffix.lower() in ['.jpg', '.png', '.gif']]

        # 打印符合条件的目录
        if illust_files and len(illust_files) == len(valid_files) and len(illust_files) >= min_count:
            print(f'{root_path.relative_to(src_dir).as_posix():<10} {len(illust_files):>5d}')


def pack_by_category(illusts: dict):
    """按分类打包专辑"""
    # 1. 每个压缩包至少包含 30 张插画


def pack_by_artist(src_dirs: list[Path], dst_dir: Path, i_min: int = 30, mode: str = 'move'):
    """按画师打包专辑"""
    artists = defaultdict(list)
    for ipath, ijson in ut.find_illust_and_json(src_dirs, JSON_DIR):
        user = ijson['user']
        artists[user['id']].append(ipath)
    artworks = [(k, len(v)) for k, v in artists.items()]

    for aid, n in artworks:
        if n > i_min:
            print(f'{aid:>10d}\t{n}')


def get_meta(asset_path: Path, catg: Category, password: str = cfg.FAV_DOMAIN):
    """获取元数据"""
    match catg:
        case Category.acg_album | Category.cos_album:
            return AlbumMeta(asset_path, catg, password)
        case Category.acg_gallery | Category.cos_gallery:
            return GalleryMeta(asset_path, catg, password)
        case _:
            raise ValueError(f'Invalid category: {catg}')


if __name__ == '__main__':
    # 分类相关参数
    catg_parser = argparse.ArgumentParser(add_help=False)
    catg_group = catg_parser.add_mutually_exclusive_group(required=True)
    catg_group.add_argument('-a', '--acg', action='store_const', const='acg', help='ACG 插画专辑')
    catg_group.add_argument('-c', '--cos', action='store_const', const='cos', help='Cosplay 专辑')
    catg_parser.add_argument('-g', '--gallery', action='store_true', help='合集')

    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument('-m', '--max', type=int, default=180, help='单个图包插画数量上限')
    parser.add_argument('-n', '--min', type=int, default=30, help='单个图包插画数量下限')

    subparsers = parser.add_subparsers(dest='command', required=True)

    # 子命令：分类
    cmd_categorize = subparsers.add_parser('categorize', help='对插画进行分类')
    cmd_categorize.add_argument('-n', dest='dry_run', action='store_true', help='仅显示结果，不执行操作')
    cmd_categorize.add_argument('src_dirs', type=Path, nargs='+', help='源目录')
    cmd_categorize.add_argument('dst_dir', type=Path, help='目标目录')

    # 子命令：自动发现类别
    cmd_discover = subparsers.add_parser('discover', help='自动发现新分类')
    cmd_discover.add_argument('-s', dest='exclude_sensitive', action='store_true', help='排除敏感词')
    cmd_discover.add_argument('-m', dest='exclude_major', action='store_true', help='排除已经存在的分类')
    cmd_discover.add_argument('-n', dest='exclude_minor', action='store_true', help='排除已经存在的子分类')
    cmd_discover.add_argument('src_dirs', type=Path, nargs='+', help='源目录')

    # 子命令：批量重命名
    cmd_rename = subparsers.add_parser('rename', help='批量重命名插画')
    pattern_group = cmd_rename.add_mutually_exclusive_group(required=True)
    pattern_group.add_argument('-e', dest='regexp', type=str, help='使用正则表达式')
    pattern_group.add_argument('-p', dest='pattern', type=str, help='要匹配的部分')
    cmd_rename.add_argument('-i', dest='ignore_case', action='store_true', help='忽略大小写(仅在使用正则时有效)')
    cmd_rename.add_argument('-r', dest='replace', type=str, required=True, help='要替换的新字符串')
    cmd_rename.add_argument('target', type=Path, help='包含待处理文件的目录')

    # 子命令：打包预处理
    cmd_prep = subparsers.add_parser('prep', parents=[catg_parser], help='打包预处理')
    cmd_prep.add_argument('-p', dest='password', type=str, default=cfg.FAV_DOMAIN, help='压缩包密码')
    cmd_prep.add_argument('-n', dest='zip_name', type=str, default='', help='压缩包名称或前缀')
    cmd_prep.add_argument('src_dir', type=Path, help='源目录')

    # 子命令：合并缩略图
    cmd_merge = subparsers.add_parser('merge', parents=[catg_parser], help='合并缩略图')
    cmd_merge.add_argument('src_dir', type=Path, help='源目录')

    # 子命令：导出元数据
    cmd_dump = subparsers.add_parser('dump', parents=[catg_parser], help='导出元数据')
    cmd_dump.add_argument('src_dir', type=Path, help='源目录')

    # 子命令：汇总元数据
    cmd_summary = subparsers.add_parser('converge', parents=[catg_parser], help='汇总元数据')
    cmd_summary.add_argument('src_dir', type=Path, help='源目录')
    args = parser.parse_args()
    try:
        match args.command:
            case 'categorize':
                categorize_illusts(args.src_dirs, args.dst_dir, args.dry_run)

            case 'discover':
                auto_discover_categories(args.src_dirs, args.exclude_sensitive, args.exclude_major, args.exclude_minor)

            case 'rename':
                rename(args.target, args.regexp, args.pattern, args.replace, args.ignore_case)

            case 'prep':
                catg = Category.get_catg(args.acg or args.cos, gallery=args.gallery)
                meta = get_meta(args.src_dir, catg, args.password)
                meta.preprocess(args.zip_name)

                print_inf('请筛检样图')
                if args.gallery:
                    for alb in meta.albums:
                        os.system(f'open {alb.meta_path!s}')  # noqa: S605
                else:
                    os.system(f'open {meta.meta_path!s}')  # noqa: S605

            case 'merge':
                catg = Category.get_catg(args.acg or args.cos, gallery=args.gallery)
                meta = get_meta(args.src_dir, catg)
                meta.auto_merge()
                os.system(f'open {meta.meta_path!s}')  # noqa: S605

            case 'dump':
                catg = Category.get_catg(args.acg or args.cos, gallery=args.gallery)
                meta = get_meta(args.src_dir, catg)
                meta.enter_empty_fields()
                meta.dump()
                os.system(f'open {meta.meta_path!s}')  # noqa: S605

            case 'converge':
                catg = Category.get_catg(args.acg or args.cos, gallery=args.gallery)
                meta = get_meta(args.src_dir, catg)
                meta.converge()

            case _:
                raise NotImplementedError(args.command)
    except KeyboardInterrupt:
        print_err('\nInterrupted by user')
