#!/bin/bash

# 输出文件路径（你可以改成你希望的路径）
PROJECT_DIR=$(readlink -f $(dirname $(dirname "$0")))
OUT_FILE="$PROJECT_DIR/config/site/CloudFlareIPs.conf"
NGINX_CONF="/etc/nginx/conf.d/CloudFlareIPs.conf"

function fetch_ip() {
    echo "# Auto-generated Cloudflare IPs"

    # 拉取 IPv4 段
    curl -s https://www.cloudflare.com/ips-v4 | while read ip; do
        echo "set_real_ip_from $ip;"
    done

    # 拉取 IPv6 段
    curl -s https://www.cloudflare.com/ips-v6 | while read ip; do
        echo "set_real_ip_from $ip;"
    done

    # 加上 real_ip_header 指令
    echo "real_ip_header CF-Connecting-IP;"
}

# 检查并重载 Nginx
function nginx_reload() {
    # 检查 nginx 是否存在
    if command -v nginx &> /dev/null; then
        # 检查输出文件是否存在
        target=$(readlink -f $NGINX_CONF)
        if [ "$target" != "$OUT_FILE" ]; then
            sudo rm -f $NGINX_CONF
            sudo ln -s $OUT_FILE $NGINX_CONF
        fi

        # 重载 Nginx 配置
        sudo nginx -t && sudo systemctl reload nginx
        echo "✅ Cloudflare IP 配置已更新并重载 Nginx"
    else
        echo "❌ Nginx 未安装，无法重载配置"
    fi


}

# 执行拉取
fetch_ip > $OUT_FILE
echo "✅ Cloudflare IP 配置已更新"

# 重载Nginx
nginx_reload
