#!/bin/bash
LOCAL_DIR=$(readlink -f $(dirname $(dirname "$0")))
REMOTE_DIR=/opt/albumd
DELETE=""

while getopts ":D" opt; do
    case $opt in
        D)
            DELETE="--delete"
            ;;
        \?)
            echo "无效的参数: -$OPTARG" >&2
            echo "Usage: upload.sh [-D]" >&2
            exit 1
            ;;
    esac
done
shift $((OPTIND-1))

echo "Syncing Files"
rsync -acvzHP $DELETE \
      --exclude={.git,.venv,.DS_Store,__pycache__,.vscode,.mypy_cache,.gitignore} \
      --exclude={avatar,cosplay,large,origin,small,logs,sitemap,misc} \
      $LOCAL_DIR/ bee:$REMOTE_DIR/

ssh bee "
    printf '\nDeleting Page Cache\n' &&
    redis-cli -n 1 --scan --pattern 'Page*'|xargs redis-cli -n 1 DEL &&
    printf '\nReloading Albumd Service\n' &&
    sudo systemctl reload albumd.service
"
echo -e "\nRelease Done"
