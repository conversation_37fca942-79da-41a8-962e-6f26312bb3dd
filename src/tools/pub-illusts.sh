#!/bin/bash

# 1. 创建大图、缩略图, 并生成扩展 JSON 数据文件
# 2. 上传到 B2
#     1. origin: ipixcc
#     2. large: ipixiv
#     3. small: ipixiv
# 4. 导入到数据库
#     1. 收集所有图片的 JSON 数据文件
#     2. 导入到数据库
#     3. 将扩展 JSON 数据文件导入到数据库


LARGE_DIR='/tmp/large'  # 大图目录
SMALL_DIR='/tmp/small'  # 缩略图目录
ALBUMD_DIR=$AL          # albumd 项目目录
JSON_DIR=$J             # JSON 数据目录
REMOTE_SERVER="bee"     # 远程服务器
FUNCTION="all"          # 要执行的功能，默认是 all，可选项: all, thumbnail, upload, collect, import

# 帮助信息
function help() {
    echo "Usage:"
    echo "    $(basename $0) [-s small_dir] [-l large_dir] [-f function] ORIGIN_DIR PN_DIR"
    echo -e "\nPositional arguments:"
    echo "    ORIGIN_DIR: 原图目录"
    echo "    PN_DIR: 附图目录"
    echo -e "\nOptions:"
    echo "    -h: 显示帮助信息"
    echo "    -s SMALL_DIR: 缩略图目录"
    echo "    -l LARGE_DIR: 大图目录"
    echo "    -f FUNCTION: 要执行的功能，可选项: all（默认）, thumbnail, upload, collect, import"
}

# 解析参数
while getopts ":hs:l:f:" opt; do
    case $opt in
        s)
            SMALL_DIR=$OPTARG
            ;;
        l)
            LARGE_DIR=$OPTARG
            ;;
        f)
            FUNCTION=$OPTARG
            ;;
        h)
            help
            exit 0
            ;;
        \?)
            echo -e "无效的参数: -$OPTARG\n" >&2
            help >&2
            exit 1
            ;;
    esac
done
shift $((OPTIND-1))


# 检查参数“原图目录”、“附图目录”
ORIGIN_DIR=$1
PN_DIR=$2
if [ -z "$ORIGIN_DIR" ] || [ -z "$PN_DIR" ]; then
    echo "Usage: upload.sh [-s small_dir] [-l large_dir] <origin_dir> <pn_dir>" >&2
    exit 2
fi

function thumbnail() {
    echo "1. 创建大图、缩略图"
    source $ALBUMD_DIR/.venv/bin/activate && \
    $ALBUMD_DIR/manage.py thumbnail -t 10 -s $SMALL_DIR -l $LARGE_DIR $ORIGIN_DIR $PN_DIR && \
    echo -e "大图、缩略图创建完成\n\n" || exit 3
}

function upload() {
    echo "2. 将所有插画上传到 B2"
    echo "   上传 $ORIGIN_DIR"
    b2 sync --threads=10 --compare-versions=size $ORIGIN_DIR b2://ipixcc/origin
    echo "   上传 $PN_DIR"
    b2 sync --threads=10 --compare-versions=size $PN_DIR b2://ipixcc/origin
    echo "   上传 $LARGE_DIR"
    b2 sync --threads=10 --compare-versions=size $LARGE_DIR b2://ipixiv/large
    echo "   上传 $SMALL_DIR"
    b2 sync --threads=10 --compare-versions=size $SMALL_DIR b2://ipixiv/small
    echo -e "插画上传完成\n\n"
}

function collect() {
    echo "3. 收集所有图片的 JSON 数据文件, 上传到服务器"
    TMPDIR=/tmp/upload_json
    rm -rf $TMPDIR && mkdir -p $TMPDIR || exit 4
    # 收集并上传所有图片的 JSON 数据文件
    for ipath in $(find $ORIGIN_DIR $PN_DIR -name '*_p*.???' -type f); do
        name=$(basename $ipath)
        jpath=$JSON_DIR/illust/${name%%_p*}.json
        [ -f $jpath ] && ln -sf $jpath $TMPDIR
    done && \
    rsync -crvzptHPL $TMPDIR/ $REMOTE_SERVER:~/src/JSON/tmp
    rm -rf $TMPDIR
    # 收集并上传扩展 JSON 数据文件
    ext_json=$(ls ExtendData-*.json | tail -1)
    [[ -f $ext_json ]] && rsync -crvzptHPL $ext_json $REMOTE_SERVER:~/src/JSON/extends/ || exit 5
    echo -e "JSON 数据上传完成\n\n"
}

function import() {
    echo "4. 调用远程脚本导入到数据库"
    ext_json=$(ls ExtendData-*.json | tail -1) || exit 6
    ssh $REMOTE_SERVER "
    source /opt/albumd/.venv/bin/activate &&
    /opt/albumd/manage.py -e prod import illust -m update ~/src/JSON/tmp &&
    /opt/albumd/manage.py -e prod import extend ~/src/JSON/extends/$ext_json &&
    mv -f ~/src/JSON/tmp/*.json ~/src/JSON/illust/ &&
    rm -rf ~/src/JSON/tmp" && echo "导入完成" || exit 7
}

# 执行功能
case $FUNCTION in
    thumbnail)
        thumbnail
        ;;
    upload)
        upload
        ;;
    collect)
        collect
        ;;
    import)
        import
        ;;
    all)
        thumbnail && upload && collect && import || exit 1
        ;;
    *)
        echo "无效的功能: $FUNCTION" >&2
        exit 8
        ;;
esac
