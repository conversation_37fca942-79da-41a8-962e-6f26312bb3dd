#!/bin/bash
# 数据库备份和还原工具
#
# 用法:
#   db-tool.sh -b BACKUP_DIR    备份数据库到指定目录
#   db-tool.sh -r BACKUP_FILE   从备份文件恢复数据库
#   db-tool.sh -c               清空 Redis 缓存
#
# 参数:
#   -b BACKUP_DIR   备份数据库, 文件名格式为 Backup_数据库名_YYYY-MM-DD.dump, 保存在 BACKUP_DIR 目录下
#   -r BACKUP_FILE  从备份文件恢复数据库, 指定 dump 文件路径
#   -c              清空 Redis 缓存 (根据环境自动选择数据库: prod=1, test=2, remote=3)
#   -e ENV          指定环境 (prod, test, remote), 优先级高于环境变量 ALBUM_ENV
#   -h              显示帮助信息

# 颜色定义
RED='\033[0;31m'
BLUE='\033[0;94m'
NC='\033[0m' # No Color

# 输出信息函数
function inf() {
    echo -e "${BLUE}[INFO] $*${NC}"
}

# 输出错误函数
function err() {
    echo -e "${RED}[ERROR] $*${NC}" >&2
}

# 数据库连接信息变量（将从配置文件读取或通过命令行参数指定）
DB_NAME=""
DB_USER=""
DB_PASSWORD=""
DB_HOST=""
DB_PORT=""
REDIS_HOST=""
REDIS_PORT=""
REDIS_DB=""

# 获取环境变量
ALBUM_ENV=${ALBUM_ENV:-test}

# 显示帮助信息
function show_help() {
    tool=$(basename "$0")
    echo "用法:"
    echo "  $tool -b BACKUP_DIR    备份数据库到指定目录"
    echo "  $tool -r BACKUP_FILE   从备份文件恢复数据库"
    echo "  $tool -c               清空 Redis 缓存"
    echo ""
    echo "参数:"
    echo "  -b BACKUP_DIR   备份数据库到指定目录 (文件名: Backup-<dbname>-YYYYMMDD.dump)"
    echo "  -r BACKUP_FILE  从备份文件恢复数据库"
    echo "  -c              清空 Redis 缓存"
    echo "  -e ENV          指定系统环境 (可选值: prod, test, remote)"
    echo "  -h              显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  ALBUM_ENV               当前环境: $ALBUM_ENV"
}

# 备份数据库
function backup_db() {
    local backup_dir="$1"
    local today=$(date +%Y%m%d)
    local backup_file="${backup_dir}/Backup-${DB_NAME}-${today}.dump"

    # 检查备份目录是否存在，不存在则创建
    if [ ! -d "$backup_dir" ]; then
        mkdir -p "$backup_dir" || { err "无法创建备份目录 $backup_dir"; exit 1; }
    fi

    inf "正在备份数据库 ${DB_NAME} 到 ${backup_file}"

    # 使用 pg_dump 备份数据库
    PGPASSWORD="$DB_PASSWORD" pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -F c \
        -Z 9 \
        -b \
        -v \
        -f "$backup_file" \
        "$DB_NAME"

    if [ $? -eq 0 ]; then
        inf "数据库备份成功: $backup_file"
    else
        err "数据库备份失败"
        exit 1
    fi
}

# 恢复数据库
function restore_db() {
    local dump_file="$1"

    # 检查备份文件是否存在
    if [ ! -f "$dump_file" ]; then
        err "备份文件 $dump_file 不存在"
        exit 1
    fi

    inf "正在从 ${dump_file} 恢复数据库 ${DB_NAME}"

    # 先删除现有连接
    inf "正在终止现有连接..."
    PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();" \
        postgres

    # 删除现有数据库并重新创建
    inf "正在删除并重新创建数据库..."
    PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -c "DROP DATABASE IF EXISTS $DB_NAME;" \
        postgres

    PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -c "CREATE DATABASE $DB_NAME;" \
        postgres

    # 使用 pg_restore 恢复数据库
    inf "正在恢复数据库..."
    PGPASSWORD="$DB_PASSWORD" pg_restore \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        -v \
        "$dump_file"

    if [ $? -eq 0 ]; then
        inf "数据库恢复成功"
    else
        err "数据库恢复可能存在问题，请检查错误信息"
    fi
}

# 根据 ALBUM_ENV 环境变量确定 Redis 数据库编号
function get_redis_db() {
    case "$ALBUM_ENV" in
        prod)
            echo 1
            ;;
        test)
            echo 2
            ;;
        remote)
            echo 3
            ;;
        *)
            err "不支持的环境变量值 ALBUM_ENV=$ALBUM_ENV。可选值 prod、test 或 remote"
            exit 1
            ;;
    esac
}

# 清空 Redis 缓存
function clear_redis_cache() {
    # 检查 Redis 连接参数
    if [ -z "$REDIS_HOST" ] || [ -z "$REDIS_PORT" ] || [ -z "$REDIS_DB" ]; then
        err "Redis 连接信息不完整，无法执行清空缓存操作"
        [ -z "$REDIS_HOST" ] && err "  - 缺少 Redis 主机名"
        [ -z "$REDIS_PORT" ] && err "  - 缺少 Redis 端口"
        [ -z "$REDIS_DB" ]   && err "  - 缺少 Redis 数据库"
        exit 1
    fi

    # 获取缓存键数量
    local keys_count=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -n "$REDIS_DB" DBSIZE)
    inf "当前缓存键数量: $keys_count"

    # 使用 redis-cli 清空指定的数据库
    inf "正在清空 Redis 数据库 $REDIS_HOST:$REDIS_PORT/$REDIS_DB"
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -n "$REDIS_DB" FLUSHDB

    if [ $? -eq 0 ]; then
        inf "Redis 缓存清空成功"

        # 验证清空结果
        keys_count=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -n "$REDIS_DB" DBSIZE)
        inf "清空后缓存键数量: $keys_count"
    else
        err "Redis 缓存清空失败"
        exit 1
    fi
}

# 从配置文件中提取 PostgreSQL 和 Redis 连接信息
function read_config() {
    inf "ALBUM_ENV: $ALBUM_ENV"
    local config_file="$(dirname "$0")/../config/server.py"

    if [ ! -f "$config_file" ]; then
        err "配置文件 $config_file 不存在"
        exit 1
    fi

    case $1 in
        db)
            # 根据环境变量提取对应的数据库连接字符串
            local db_conn=$(grep -A 4 -E "^DB_CONNS" "$config_file" | grep "'$ALBUM_ENV'" | grep -o "'postgres://[^']*'" | tr -d "'")
            if [ -n "$db_conn" ]; then
                # 提取用户名
                local user=$(echo $db_conn | sed -n 's/postgres:\/\/\([^:]*\):.*/\1/p')
                [ -n "$user" ] && DB_USER=$user

                # 提取密码
                local password=$(echo $db_conn | sed -n 's/postgres:\/\/[^:]*:\([^@]*\)@.*/\1/p')
                [ -n "$password" ] && DB_PASSWORD=$password

                # 提取主机
                local host=$(echo $db_conn | sed -n 's/postgres:\/\/[^@]*@\([^:]*\):.*/\1/p')
                [ -n "$host" ] && DB_HOST=$host

                # 提取端口
                local port=$(echo $db_conn | sed -n 's/postgres:\/\/[^@]*@[^:]*:\([^\/]*\)\/.*/\1/p')
                [ -n "$port" ] && DB_PORT=$port

                # 提取数据库名
                local dbname=$(echo $db_conn | sed -n 's/postgres:\/\/[^\/]*\/\(.*\)/\1/p')
                [ -n "$dbname" ] && DB_NAME=$dbname

                inf "PG 数据库: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
            else
                err "无法从配置文件中提取数据库连接信息"
            fi
            ;;
        redis)
            # 提取 Redis 配置
            local redis_host=$(grep -A 4 "REDIS" "$config_file" | grep "host" | grep -o "'[^']*'" | tr -d "'" | tail -1)
            [ -n "$redis_host" ] && REDIS_HOST=$redis_host

            local redis_port=$(grep -A 4 "REDIS" "$config_file" | grep "port" | grep -o "[0-9]*" | head -1)
            [ -n "$redis_port" ] && REDIS_PORT=$redis_port

            local redis_db=$(get_redis_db)
            [ -n "$redis_db" ] && REDIS_DB=$redis_db

            if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ] && [ -n "$REDIS_DB" ]; then
                inf "Redis连接: $REDIS_HOST:$REDIS_PORT/$REDIS_DB"
            fi
            ;;
        *)
            err "无效的参数: $1"
            exit 1
            ;;
    esac
}

# 检查 ALBUM_ENV 是否有效
function check_album_env() {
    case "$1" in
        prod|test|remote)
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# 主函数
function main() {
    local OPERATION=""
    local OPERATION_ARG=""

    # 解析参数
    while getopts ":he:b:r:c" opt; do
        case $opt in
            h)  # 显示帮助信息
                OPERATION='help'
                ;;
            e)  # 设置环境
                if check_album_env "$OPTARG"; then
                    ALBUM_ENV="$OPTARG"
                else
                    err "无效的 ALBUM_ENV 值: ${OPTARG}，可选值 prod, test, remote"
                    exit 1
                fi
                ;;
            b)  # 备份数据库到指定目录
                if [ -z "$OPERATION" ]; then
                    OPERATION="backup_db"
                    OPERATION_ARG="$OPTARG"
                fi
                ;;
            r)  # 从备份文件恢复数据库
                if [ -z "$OPERATION" ]; then
                    OPERATION="restore_db"
                    OPERATION_ARG="$OPTARG"
                fi
                ;;
            c)  # 清空 Redis 缓存
                if [ -z "$OPERATION" ]; then
                    OPERATION="clear_cache"
                fi
                ;;
            \?) # 处理无效选项
                err "无效的选项 -$OPTARG"
                show_help
                exit 1
                ;;
            :)  # 处理缺少参数的选项
                err "选项 -$OPTARG 需要参数"
                show_help
                exit 1
                ;;
        esac
    done
    shift $((OPTIND-1))

    # 执行操作
    case "$OPERATION" in
        backup_db)
            read_config db && backup_db "$OPERATION_ARG"
            ;;
        restore_db)
            read_config db && restore_db "$OPERATION_ARG"
            ;;
        clear_cache)
            read_config redis && clear_redis_cache
            ;;
        help)
            show_help
            exit 0
            ;;
        "")
            if [ $OPTIND -eq 1 ]; then
                err "未指定操作，请提供 -b、-r 或 -c 参数"
                show_help
                exit 1
            fi
            ;;
    esac
}

# 执行主函数
main "$@"
