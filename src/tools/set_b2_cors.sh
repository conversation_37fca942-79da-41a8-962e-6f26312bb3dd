#!/bin/bash

BUCKET=$1
if [ -z "$BUCKET" ]; then
    echo "Usage: $(basename $0) <bucket_name>"
    exit 1
fi

b2 bucket update $BUCKET --cors-rules '[
    {
        "corsRuleName": "allowPixivDownload",
        "allowedOrigins": [
            "http://*.pixcc.net",
            "https://*.pixcc.net",
            "http://localhost:8000"
        ],
        "allowedOperations": [
            "b2_download_file_by_name",
            "b2_download_file_by_id",
            "b2_upload_file",
            "b2_upload_part",
            "s3_delete",
            "s3_get",
            "s3_head",
            "s3_put"
       ],
        "allowedHeaders": [
            "authorization",
            "range",
            "content-type"
        ],
        "exposeHeaders": ["x-bz-content-sha1"],
        "maxAgeSeconds": 86400
    }
]'
