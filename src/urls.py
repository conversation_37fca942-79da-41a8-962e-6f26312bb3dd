from fastapi import FastAPI

import apps.album.views as v_album
import apps.payment.views as v_payment
import apps.pixiv.views as v_pixiv
import apps.user.apis as a_user
import apps.user.views as v_user
from apps import privacy
from libs.http import jsonify

# from apps.pixiv import apis as p_api
# from common.utils import timer


URLS: list[tuple] = [
    # Pixiv Views
    ('GET', '/', v_pixiv.home),
    ('GET', '/w/home/', v_pixiv.home),
    ('GET', '/w/search/', v_pixiv.search),
    ('GET', '/w/illust/', v_pixiv.illust_rcmd),
    ('GET', '/w/illust/{iid}', v_pixiv.illust),
    ('GET', '/w/irelated/{iid}', v_pixiv.irelated),
    ('GET', '/w/artist/', v_pixiv.artist_rcmd),
    ('GET', '/w/artist/{aid}', v_pixiv.artist),
    ('GET', '/w/tag/', v_pixiv.tag_rcmd),
    ('GET', '/w/tag/{tid}', v_pixiv.tag),
    ('GET', '/w/album/', v_album.album_rcmd),
    ('GET', '/w/album/{alb_id}', v_album.album),
    ('GET', '/w/album/purchase/{alb_id}', v_album.purchase_album),
    # API
    # ('GET', '/a/pixiv/illust/{iid}', p_api.idetail, jsonify),
    ('GET', '/a/user/vcode/', a_user.send_verify_code, jsonify),
    ('GET', '/a/illust/download/{iid}', v_pixiv.download_illust, jsonify),
    # User Views
    ('POST', '/w/user/register/', v_user.signup, jsonify),
    ('GET', '/w/user/register/', v_user.signup),
    ('POST', '/w/user/login/', v_user.signin),
    ('GET', '/w/user/login/', v_user.signin),
    ('GET', '/w/user/logout/', v_user.signout),
    ('POST', '/w/user/profile/', v_user.profile, jsonify),
    ('GET', '/w/user/profile/', v_user.profile),
    ('GET', '/w/user/illusts/', v_user.purchased_illusts),
    ('GET', '/w/user/albums/', v_user.purchased_albums),
    ('GET', '/w/user/checkin/', v_user.checkin, jsonify),
    ('GET', '/w/user/followed/', v_user.followed),
    ('POST', '/a/user/follow/{aid}', v_user.follow, jsonify),
    ('POST', '/a/user/unfollow/{aid}', v_user.unfollow, jsonify),
    ('GET', '/w/user/favorited/', v_user.favorited),
    ('POST', '/a/user/favorite/{iid}', v_user.favorite, jsonify),
    ('POST', '/a/user/unfavorite/{iid}', v_user.unfavorite, jsonify),
    # Payment Views
    ('GET', '/w/purchase/', v_payment.products),
    ('GET', '/w/purchase/vip/{vid}/', v_payment.purchase_vip),
    ('GET', '/w/purchase/coin/{cid}/', v_payment.purchase_coin),
    # ('GET', '/w/purchase/album/{aid}/', v_payment.purchase_album),
    ('GET', '/a/purchase/callback/', v_payment.purchase_callback, jsonify),
    ('GET', '/w/purchase/result/', v_payment.purchase_result),
    # privacy
    ('GET', '/w/privacy/', privacy),
]


def register_urls(app: FastAPI, urls: list):
    """绑定 URL 与 视图函数"""
    for method, url_path, view_fn, *decos in urls:
        http_method = getattr(app, method.lower())
        fn_bind = http_method(url_path)

        # 用指定的装饰器包装视图函数
        for deco in decos:
            view_fn = deco(view_fn)

        # 绑定视图函数与路由
        fn_bind(view_fn)
