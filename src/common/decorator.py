import asyncio
import functools
import inspect
import logging

from fastapi import Request

from libs.cache import redis

inf = logging.getLogger('info')
err = logging.getLogger('error')


def bg_task(delay: float = 0):
    """后台任务装饰器"""

    async def async_holder():
        pass

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            async def runner():
                if delay > 0:
                    await asyncio.sleep(delay)
                # 检查是否是协程函数
                if asyncio.iscoroutinefunction(func):
                    await func(*args, **kwargs)
                else:
                    func(*args, **kwargs)
                inf.info(f'BackgroundTask {func.__name__} finished')

            try:
                asyncio.create_task(runner())  # noqa: RUF006
            except Exception as e:
                err.error(f'BackgroundTask {func.__name__} failed: {e}')

            if asyncio.iscoroutinefunction(func):
                return async_holder()

        return wrapper

    return decorator


def is_human_user(request: Request) -> bool:
    ua = request.headers.get('user-agent', '').lower()

    if not ua:
        return False  # 没 UA 的绝大多数是脚本或爬虫

    # 典型爬虫 / 扫描器 / SEO工具 / 监控系统 / 安全审计
    known_bots = [
        'bot',
        'spider',
        'crawler',
        'scanner',
        'python',
        'curl',
        'wget',
        'libwww',
        'httpclient',
        'go-http-client',
        'java',
        'fetch',
        'node-fetch',
        'httpx',
        'axios',
        'perl',
        'ruby',
        'scrapy',
        'php',
        'monitor',
        # 主流搜索引擎爬虫、 SEO、站点分析类
        'slurp',
        'baiduspider',
        'yandex',
        'sogou',
        'siteaudit',
        'linkdex',
        'lighthouse',
        # 安全/爬虫平台
        'zgrab',
        'nmap',
        'masscan',
        'shodan',
        'censys',
        'wprecon',
    ]

    if any(bot in ua for bot in known_bots):
        return False
    return True


def page_hits(key: str, name: str, *ext_keys: str, score=1):
    """页面访问统计装饰器"""

    def decorator(func):
        rank_key = f'Hit::{key.strip().title()}'

        @functools.wraps(func)
        async def wrapper(request, *args, **kwargs):
            params = inspect.signature(func).bind(request, *args, **kwargs)
            params.apply_defaults()
            if is_human_user(request):
                if ext_keys:
                    nonlocal rank_key
                    params_key = "_".join(params.arguments.get(ek, "").strip() for ek in ext_keys)
                    rank_key = f'Hit::{key.strip().title()}:{params_key}'

                # 通过 Redis 的 ZSET 类型来记录访问次数
                if identity := params.arguments.get(name):
                    await redis.zincrby(rank_key, score, identity)

            return await func(request, *args, **kwargs)

        return wrapper

    return decorator
