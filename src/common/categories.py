import re
from collections import Counter, OrderedDict

from common.utils import readable_tag, replace_cn_punctuation

__all__ = [
    'MajorCategories',
    'MinorCategories',
    'match_major_categories',
    'match_minor_categories',
    'match_tag_category',
]

Categories = OrderedDict[str, re.Pattern]

MajorCategories: Categories = OrderedDict([
    ('国漫', re.compile(r'斗破苍穹|完美世界|仙逆|不良人|天行九歌|琅琊榜|国漫|中国风', re.IGNORECASE)),
    ('出包王女', re.compile(r'出包王女', re.IGNORECASE)),
    ('银魂', re.compile(r'银魂', re.IGNORECASE)),
    ('恶魔战士', re.compile(r'恶魔战士', re.IGNORECASE)),
    ('罪恶装备', re.compile(r'罪恶装备', re.IGNORECASE)),
    ('时光代理人', re.compile(r'时光代理人', re.IGNORECASE)),
    ('上低音号', re.compile(r'吹响.*?低音号', re.IGNORECASE)),
    ('薇薇', re.compile(r'\bVivy\b|薇薇', re.IGNORECASE)),
    ('东京食尸鬼', re.compile(r'东京食尸鬼|东京喰种', re.IGNORECASE)),
    ('小埋', re.compile(r'干物妹.*?小埋', re.IGNORECASE)),
    ('樱花庄的宠物女孩', re.compile(r'樱花庄的宠物女孩|椎名真白', re.IGNORECASE)),
    ('地下城与勇士', re.compile(r'地下城与勇士|アラド戦記|Dungeon.*?Fighter', re.IGNORECASE)),
    ('美少女战士', re.compile(r'美少女战士', re.IGNORECASE)),
    ('弹丸论破', re.compile(r'弹丸论破|枪弹辩驳|ダンガンロンパ|七海千秋', re.IGNORECASE)),
    ('艾莉同学', re.compile(r'艾莉同学', re.IGNORECASE)),
    ('捡到女高中生', re.compile(r'捡到女高中生|荻原沙优', re.IGNORECASE)),
    ('春日野穹', re.compile(r'缘之空|春日野穹', re.IGNORECASE)),
    ('宝可梦', re.compile(r'宝可梦|pokemon|pokémon', re.IGNORECASE)),
    ('Highspeed', re.compile(r'Highspeed|Étoile', re.IGNORECASE)),
    ('末日三问', re.compile(r'末日.*?做什么|What.*?End.*?World.*?Busy.*?Save', re.IGNORECASE)),
    ('魂器学院', re.compile(r'魂器学院', re.IGNORECASE)),
    ('八奈见杏菜', re.compile(r'八奈见杏菜', re.IGNORECASE)),
    ('转生变成史莱姆', re.compile(r'转生.*?史莱姆|利姆鲁', re.IGNORECASE)),
    ('无期迷途', re.compile(r'无期迷途', re.IGNORECASE)),
    ('炼金工房', re.compile(r'炼金工房', re.IGNORECASE)),
    ('AngelBeats', re.compile(r'Angel.*?Beats|立华奏', re.IGNORECASE)),
    ('黑暗之魂', re.compile(r'黑暗之魂|ダークソウル|Dark.*?Souls', re.IGNORECASE)),
    ('影之诗', re.compile(r'影之诗', re.IGNORECASE)),
    ('白夜极光', re.compile(r'白夜极光|Alchemy.*?Stars', re.IGNORECASE)),
    ('Arcaea', re.compile(r'韵律源点|アーケア|Arcaea', re.IGNORECASE)),
    ('炽焰天穹', re.compile(r'炽焰天穹|ヘブンバーンズレッド|Heaven.*?Burns.*?Red', re.IGNORECASE)),
    ('无职转生', re.compile(r'无职転生|无职转生', re.IGNORECASE)),
    ('琅琊榜', re.compile(r'琅琊榜|琰殊|靖苏', re.IGNORECASE)),
    ('火焰之纹章', re.compile(r'火焰之纹章|圣火降魔录', re.IGNORECASE)),
    ('我家孩子', re.compile(r'我家孩子', re.IGNORECASE)),
    ('赛博朋克', re.compile(r'赛博朋克', re.IGNORECASE)),
    ('幻塔', re.compile(r'幻塔', re.IGNORECASE)),
    ('名侦探柯南', re.compile(r'柯南|工藤新一|毛利兰|灰原哀', re.IGNORECASE)),
    ('机械少女', re.compile(r'机械少女|人造人', re.IGNORECASE)),
    ('塞尔达传说', re.compile(r'塞尔达|林克', re.IGNORECASE)),
    ('为美好的世界献上祝福', re.compile(r'为美好的世界献上祝福', re.IGNORECASE)),
    ('BanG', re.compile(r'BanG.*?Dream', re.IGNORECASE)),
    ('魔法少女小圆', re.compile(r'魔法少女小圆', re.IGNORECASE)),
    ('守望先锋', re.compile(r'守望先锋', re.IGNORECASE)),
    ('路人女主的养成方法', re.compile(r'路人女主|加藤惠', re.IGNORECASE)),
    ('境界的彼方', re.compile(r'境界的彼方|栗山未来', re.IGNORECASE)),
    ('碧蓝幻想', re.compile(r'碧蓝幻想|Granblue', re.IGNORECASE)),
    ('刀剑乱舞', re.compile(r'刀剑乱舞', re.IGNORECASE)),
    ('战舰少女', re.compile(r'战舰少女', re.IGNORECASE)),
    ('血小板', re.compile(r'工作细胞|血小板', re.IGNORECASE)),
    ('2233娘', re.compile(r'2233\b|\b2233娘|\b22娘|\b33娘|\bbilibili\b|哔哩哔哩', re.IGNORECASE)),
    ('冰菓', re.compile(r'冰菓|千反田爱瑠', re.IGNORECASE)),
    ('克苏鲁', re.compile(r'克苏鲁|巨大生物|怪物', re.IGNORECASE)),
    ('王者荣耀', re.compile(r'王者荣耀', re.IGNORECASE)),
    ('租借女友', re.compile(r'租借女友|Rent.*?Girlfriend|水原千鹤', re.IGNORECASE)),
    ('游戏王', re.compile(r'游戏王', re.IGNORECASE)),
    ('地缚少年花子君', re.compile(r'地缚少年花子君|Hanako.*?kun|八寻宁', re.IGNORECASE)),
    ('友利奈绪', re.compile(r'Charlotte|友利奈绪', re.IGNORECASE)),
    ('轻音少女', re.compile(r'轻音少女', re.IGNORECASE)),
    ('尘白禁区', re.compile(r'尘白禁区', re.IGNORECASE)),
    ('某科学的超电磁炮', re.compile(r'科学.*?超电磁炮|御坂美琴', re.IGNORECASE)),
    ('电锯人', re.compile(r'电锯人|链锯人', re.IGNORECASE)),
    ('咒术回战', re.compile(r'咒术回|五条悟', re.IGNORECASE)),
    ('樱岛麻衣', re.compile(r'猪头少年.*?兔女郎|樱岛麻衣', re.IGNORECASE)),
    ('新海诚', re.compile(r'新海诚|天气之子|你的名字|星之声|言叶之庭|秒速五厘米|追逐繁星的孩子|云之彼', re.IGNORECASE)),
    ('口袋妖怪', re.compile(r'口袋妖怪', re.IGNORECASE)),
    ('中二病也要谈恋爱', re.compile(r'中二病也要谈恋爱|小鸟游六花', re.IGNORECASE)),
    ('幼女战记', re.compile(r'幼女战记|谭雅战记|幼女战纪', re.IGNORECASE)),
    ('我推的孩子', re.compile(r'我推的孩子|星野アイ|星野爱', re.IGNORECASE)),
    ('春物', re.compile(r'春物|我的青春恋爱喜剧|雪之下雪乃', re.IGNORECASE)),
    ('孤独摇滚', re.compile(r'孤独摇滚', re.IGNORECASE)),
    ('龙王的工作', re.compile(r'龙王的工作', re.IGNORECASE)),
    ('古立特', re.compile(r'古立特|宝多六花', re.IGNORECASE)),
    ('异度神剑', re.compile(r'异度神剑', re.IGNORECASE)),
    ('魔卡少女樱', re.compile(r'魔卡少女樱|木之本樱', re.IGNORECASE)),
    ('紫罗兰永恒花园', re.compile(r'紫罗兰永恒花园|薇尔莉特', re.IGNORECASE)),
    ('阴阳师', re.compile(r'阴阳师', re.IGNORECASE)),
    ('刀剑神域', re.compile(r'刀剑神域|\bSAO\b|亚丝娜', re.IGNORECASE)),
    ('埃罗芒阿老师', re.compile(r'埃罗芒阿老师|和泉纱雾', re.IGNORECASE)),
    ('尼尔·机械纪元', re.compile(r'\bNieR|\b尼尔(:|自动人形|机械纪元)*?|寄叶.*?型', re.IGNORECASE)),
    ('鬼灭之刃', re.compile(r'鬼灭之刃|蝴蝶忍|祢豆子', re.IGNORECASE)),
    ('龙珠', re.compile(r'龙珠', re.IGNORECASE)),
    ('战双帕弥什', re.compile(r'战双帕弥什|Punishing', re.IGNORECASE)),
    ('最终幻想', re.compile(r'最终幻想|FINALFANTASY|蒂法', re.IGNORECASE)),
    ('比翼之吻', re.compile(r'Darling.*?Franxx', re.IGNORECASE)),
    ('葬送的芙莉莲', re.compile(r'葬送のフリーレン|葬送的芙莉莲', re.IGNORECASE)),
    ('LoveLive', re.compile(r'Love.*?Live|唐可可', re.IGNORECASE)),
    ('约会大作战', re.compile(r'约会大作战|狂三', re.IGNORECASE)),
    ('魔女之旅', re.compile(r'魔女之旅|伊蕾娜', re.IGNORECASE)),
    ('世界计划', re.compile(r'世界计划', re.IGNORECASE)),
    ('舰队Collection', re.compile(r'舰队Collection|舰娘|舰C\b', re.IGNORECASE)),
    ('间谍过家家', re.compile(r'spy.*?family|间谍过家家', re.IGNORECASE)),
    ('更衣人偶坠入爱河', re.compile(r'更衣人偶坠入爱河|喜多川海梦', re.IGNORECASE)),
    ('五等分花嫁', re.compile(r'五等分.*?(花嫁|新娘)|中野(一花|二乃|三玖|四叶|五月)', re.IGNORECASE)),
    ('莉可丽丝', re.compile(r'莉可丽丝|Lycoris|千束|泷奈', re.IGNORECASE)),
    ('NIKKE胜利女神', re.compile(r'NIKKE\b|胜利女神|Goddess.*?Victory', re.IGNORECASE)),
    ('辉夜大小姐想让我告白', re.compile(r'辉夜大小姐|四宫辉夜|Kaguya-sama', re.IGNORECASE)),
    ('重返未来1999', re.compile(r'重返未来.*?1999|Reverse.*?1999', re.IGNORECASE)),
    ('EVA', re.compile(r'新世纪福音战士|绫波丽|明日香', re.IGNORECASE)),
    ('少女前线', re.compile(r'少女前线|Girls.*?Frontline', re.IGNORECASE)),
    ('点兔', re.compile(r'点兔|请问您今天要来点兔子吗|香风智乃|保登心爱', re.IGNORECASE)),
    ('公主连结', re.compile(r'公主连结|Re.*?Dive', re.IGNORECASE)),
    ('鸣潮', re.compile(r'鸣潮|Wuthering.*?Waves', re.IGNORECASE)),
    ('从零开始的异世界生活', re.compile(r'从零开始的异世界生活|re.*?zero|[雷蕾拉]姆|爱蜜莉雅', re.IGNORECASE)),
    ('赛马娘', re.compile(r'赛马娘', re.IGNORECASE)),
    ('偶像大师', re.compile(r'偶像大师|Idolmaster|灰姑娘女孩|涩谷凛', re.IGNORECASE)),
    ('绝区零', re.compile(r'绝区零|Zenless.*?Zone.*?Zero|\bzzz\b|ゼンゼロ', re.IGNORECASE)),
    ('东方Project', re.compile(r'东方Project|东方', re.IGNORECASE)),
    ('VOCALOID', re.compile(r'\bVOCALOID|初音未来|初音|洛天依|巡音流歌', re.IGNORECASE)),
    ('崩坏学园', re.compile(r'崩坏3|崩坏3rd|Honkai.*?Impact|崩坏(学园|学院)', re.IGNORECASE)),
    ('Fate', re.compile(r'Fate\b|命运.*?冠位指定|\bFGO\b', re.IGNORECASE)),
    ('碧蓝航线', re.compile(r'碧蓝航线|Azur.*?Lane', re.IGNORECASE)),
    ('明日方舟', re.compile(r'明日方舟|Arknights', re.IGNORECASE)),
    ('星穹铁道', re.compile(r'星穹铁道|Honkai.*?Star.*?Rail|スターレイル', re.IGNORECASE)),
    ('虚拟主播', re.compile(r'虚拟主播|Hololive|彩虹社|VTuber|YouTuber', re.IGNORECASE)),
    ('蔚蓝档案', re.compile(r'[碧蔚]蓝档案|Blue.*?Archive', re.IGNORECASE)),
    ('原神', re.compile(r'原神|Genshin.*?Impact', re.IGNORECASE)),
    ('原创', re.compile(r'原创|Original|\bOC\b', re.IGNORECASE)),
])

Subjects: Categories = OrderedDict([
    ('洗澡', re.compile(r'温泉|洗澡|入浴|水浴|浴巾', re.IGNORECASE)),
    ('捆绑', re.compile(r'[紧束]缚|捆绑', re.IGNORECASE)),
    ('龙娘', re.compile(r'[角龙]娘', re.IGNORECASE)),
    ('婚纱', re.compile(r'婚纱|新娘', re.IGNORECASE)),
    ('后背', re.compile(r'露肩|肩甲骨|露背|后背', re.IGNORECASE)),
    ('南半球', re.compile(r'南半.*?球', re.IGNORECASE)),
    ('掀起裙子', re.compile(r'掀起裙子', re.IGNORECASE)),
    ('绝对领域', re.compile(r'绝对领域', re.IGNORECASE)),
    ('体操服', re.compile(r'体操服', re.IGNORECASE)),
    ('护士', re.compile(r'护士', re.IGNORECASE)),
    ('OL', re.compile(r'\bOL\b', re.IGNORECASE)),
    ('鸭子坐', re.compile(r'鸭子坐', re.IGNORECASE)),
    ('极上女体', re.compile(r'极上女体', re.IGNORECASE)),
    ('贫乳', re.compile(r'平胸|贫乳', re.IGNORECASE)),
    ('旗袍', re.compile(r'旗袍', re.IGNORECASE)),
    ('兔女郎', re.compile(r'兔女郎', re.IGNORECASE)),
    ('屁股', re.compile(r'臀部|神臀|屁股|尻神|美尻', re.IGNORECASE)),
    ('异色瞳', re.compile(r'异色瞳', re.IGNORECASE)),
    ('可爱', re.compile(r'可爱', re.IGNORECASE)),
    ('腋下', re.compile(r'腋', re.IGNORECASE)),
    ('热裤', re.compile(r'热裤', re.IGNORECASE)),
    ('内衣', re.compile(r'内衣|胸罩', re.IGNORECASE)),
    ('雪糕', re.compile(r'白.*?袜|白丝|雪糕', re.IGNORECASE)),
    ('黑丝', re.compile(r'黑.*?袜|黑丝', re.IGNORECASE)),
    ('女仆', re.compile(r'女仆', re.IGNORECASE)),
    ('百合', re.compile(r'百合|姐妹', re.IGNORECASE)),
    ('双马尾', re.compile(r'双马尾', re.IGNORECASE)),
    ('美甲', re.compile(r'美甲', re.IGNORECASE)),
    ('泳衣', re.compile(r'泳[装衣圈池]|比基尼', re.IGNORECASE)),
    ('萝莉', re.compile(r'萝莉|洛丽塔', re.IGNORECASE)),
    ('大腿', re.compile(r'[大美好郎]腿', re.IGNORECASE)),
    ('白毛', re.compile(r'[白银]发', re.IGNORECASE)),
    ('肚脐', re.compile(r'肚[脐子]|腹部', re.IGNORECASE)),
    ('兽娘', re.compile(r'[兽猫狐兔虎狼犬][耳娘]', re.IGNORECASE)),
    ('胖次', re.compile(r'胖次|内裤', re.IGNORECASE)),
    ('JK制服', re.compile(r'制服|\bJK\b|水手服|女高中生|女校学生', re.IGNORECASE)),
    ('玉足', re.compile(r'[赤裸素恋舔挠][足脚]|脚[底指趾]|足汤', re.IGNORECASE)),
    ('欧派', re.compile(r'欧派|[巨爆美]乳|极上乳房|诱人把玩的乳房|圧倒的胸', re.IGNORECASE)),
    ('风景', re.compile(r'[风背场夜雪]景|景色|星空', re.IGNORECASE)),
])

MinorCategories: dict[str, Categories] = {
    'EVA': OrderedDict([
        ('绫波丽', re.compile(r'绫波丽|Rei.*?Ayanami', re.IGNORECASE)),
        ('明日香', re.compile(r'明日香|Asuka.*?Langley', re.IGNORECASE)),
    ]),
    '鸣潮': OrderedDict([
        ('坎特蕾拉', re.compile(r'坎特蕾拉|Cantarella', re.IGNORECASE)),
        ('白芷', re.compile(r'白芷|Baizhi', re.IGNORECASE)),
        ('珂莱塔', re.compile(r'珂莱塔|Carlotta', re.IGNORECASE)),
        ('丹瑾', re.compile(r'丹瑾|丹槿|Danjin', re.IGNORECASE)),
        ('散华', re.compile(r'散华|Sanhua', re.IGNORECASE)),
        ('秧秧', re.compile(r'秧秧|Yangyang', re.IGNORECASE)),
        ('折枝', re.compile(r'折枝|Zhezhi', re.IGNORECASE)),
        ('漂泊者', re.compile(r'漂泊者|Rover', re.IGNORECASE)),
        ('守岸人', re.compile(r'守岸人|Shore.*?keeper', re.IGNORECASE)),
        ('吟霖', re.compile(r'吟霖|Yinlin', re.IGNORECASE)),
        ('椿', re.compile(r'\b椿\b|Camellya|Tsubaki', re.IGNORECASE)),
        ('今汐', re.compile(r'今汐|Jinhsi', re.IGNORECASE)),
        ('长离', re.compile(r'长离|Changli', re.IGNORECASE)),
    ]),
    '绝区零': OrderedDict([
        ('苍角', re.compile(r'苍角|Soukaku', re.IGNORECASE)),
        ('薇薇安', re.compile(r'薇薇安|Vivian|ビビアン', re.IGNORECASE)),
        ('可琳', re.compile(r'可琳|卡林|Corin|Wickes', re.IGNORECASE)),
        ('丽娜', re.compile(r'丽娜|Alexandrina|Sebastiane|Rina', re.IGNORECASE)),
        ('凯撒', re.compile(r'凯撒|Caesar', re.IGNORECASE)),
        ('格莉丝', re.compile(r'格莉丝|Grace|Howard', re.IGNORECASE)),
        ('铃', re.compile(r'\b铃\b|Belle|\bRin\b', re.IGNORECASE)),
        ('猫又', re.compile(r'猫又|猫宫又奈|Nekomiya', re.IGNORECASE)),
        ('青衣', re.compile(r'青衣|Qingyi', re.IGNORECASE)),
        ('露西', re.compile(r'露西|\bLucy\b', re.IGNORECASE)),
        ('安比', re.compile(r'安比|Anby|Demara', re.IGNORECASE)),
        ('耀嘉音', re.compile(r'耀嘉音|Astra|Yao|アストラ', re.IGNORECASE)),
        ('朱鸢', re.compile(r'朱鸢|ZhuYuan', re.IGNORECASE)),
        ('柏妮思', re.compile(r'柏妮思|怀特|Burnice', re.IGNORECASE)),
        ('月城柳', re.compile(r'\b柳\b|月城柳|Tsukishiro|Yanagi', re.IGNORECASE)),
        ('伊芙琳', re.compile(r'伊芙琳|Evelyn|イヴリン', re.IGNORECASE)),
        ('妮可', re.compile(r'妮可|Nicole', re.IGNORECASE)),
        ('简·杜', re.compile(r'\b简\b|简.*?杜|\bJane|Doe\b|ジェーン', re.IGNORECASE)),
        ('星见雅', re.compile(r'\b雅\b|星见雅|Miyabi', re.IGNORECASE)),
        ('艾莲·乔', re.compile(r'艾莲|\bEllen|Joe\b|Eren\b|エレン', re.IGNORECASE)),
    ]),
    '东方Project': OrderedDict([
        ('古明地觉', re.compile(r'古明地觉', re.IGNORECASE)),
        ('射命丸文', re.compile(r'射命丸文', re.IGNORECASE)),
        ('爱丽丝', re.compile(r'爱丽丝', re.IGNORECASE)),
        ('蕾米莉亚', re.compile(r'蕾米莉亚', re.IGNORECASE)),
        ('琪露诺', re.compile(r'琪露诺', re.IGNORECASE)),
        ('帕秋莉', re.compile(r'帕秋莉', re.IGNORECASE)),
        ('魂魄妖梦', re.compile(r'魂魄妖梦', re.IGNORECASE)),
        ('东风谷早苗', re.compile(r'东风谷早苗', re.IGNORECASE)),
        ('古明地恋', re.compile(r'古明地恋', re.IGNORECASE)),
        ('十六夜咲夜', re.compile(r'十六夜咲夜', re.IGNORECASE)),
        ('雾雨魔理沙', re.compile(r'雾雨魔理沙', re.IGNORECASE)),
        ('蕾米莉亚', re.compile(r'蕾米莉亚', re.IGNORECASE)),
        ('芙兰朵露', re.compile(r'芙兰朵露', re.IGNORECASE)),
        ('博丽灵梦', re.compile(r'博丽灵梦', re.IGNORECASE)),
    ]),
    'VOCALOID': OrderedDict([
        ('初音未来', re.compile(r'初音|\bMiku\b', re.IGNORECASE)),
        ('巡音流歌', re.compile(r'巡音流歌|\bLuka\b', re.IGNORECASE)),
        ('洛天依', re.compile(r'洛天依', re.IGNORECASE)),
    ]),
    '崩坏学园': OrderedDict([
        ('帕朵菲莉丝', re.compile(r'帕朵菲莉丝|Pardofelis', re.IGNORECASE)),
        ('瑟莉姆', re.compile(r'瑟莉姆|Thelema', re.IGNORECASE)),
        ('松雀', re.compile(r'松雀|玛丽娅|Songque', re.IGNORECASE)),
        ('薇塔', re.compile(r'薇塔|Vita', re.IGNORECASE)),
        ('无量塔姬子', re.compile(r'无量塔姬子|Himeko', re.IGNORECASE)),
        ('格蕾修', re.compile(r'格蕾修|Griseo', re.IGNORECASE)),
        ('幽兰黛尔', re.compile(r'幽兰黛尔|杜兰达尔|Durandal', re.IGNORECASE)),
        ('梅比乌斯', re.compile(r'梅比乌斯|メビウス|Mobius', re.IGNORECASE)),
        ('德丽莎', re.compile(r'德丽莎|德莉莎|Theresa|\bBoo\b', re.IGNORECASE)),
        ('丽塔', re.compile(r'丽塔|Rita', re.IGNORECASE)),
        ('八重樱', re.compile(r'八重樱|Sakura', re.IGNORECASE)),
        ('符华', re.compile(r'符华|云墨丹心|识之律者|Fu.*?Hua', re.IGNORECASE)),
        ('爱莉希雅', re.compile(r'爱莉希雅|Elysia|人之律者|Ellicia', re.IGNORECASE)),
        ('雷电芽衣', re.compile(r'雷电芽衣|雷之律者|RaidenMei', re.IGNORECASE)),
        ('琪亚娜', re.compile(r'琪亚娜|薪炎之律者|终焉之律者|空之律者|Kiana', re.IGNORECASE)),
        ('布洛妮娅', re.compile(r'布洛妮娅|布洛尼亚|Bronya|理之律者|板鸭', re.IGNORECASE)),
        ('希儿', re.compile(r'希儿|Seele|死生之律者', re.IGNORECASE)),
    ]),
    '碧蓝航线': OrderedDict([
        ('奇尔沙治', re.compile(r'奇尔沙治', re.IGNORECASE)),
        ('新泽西', re.compile(r'新泽西', re.IGNORECASE)),
        ('怨仇', re.compile(r'怨仇', re.IGNORECASE)),
        ('镇海', re.compile(r'镇海', re.IGNORECASE)),
        ('翔鹤', re.compile(r'翔鹤|瑞鹤', re.IGNORECASE)),
        ('兴登堡', re.compile(r'兴登堡', re.IGNORECASE)),
        ('安克雷奇', re.compile(r'安克雷奇', re.IGNORECASE)),
        ('雅努斯', re.compile(r'雅努斯', re.IGNORECASE)),
        ('圣路易斯', re.compile(r'圣路易斯', re.IGNORECASE)),
        ('英仙座', re.compile(r'英仙座', re.IGNORECASE)),
        ('埃吉尔', re.compile(r'埃吉尔', re.IGNORECASE)),
        ('巴尔的摩', re.compile(r'巴尔的摩', re.IGNORECASE)),
        ('小天城', re.compile(r'小天城', re.IGNORECASE)),
        ('高雄', re.compile(r'高雄', re.IGNORECASE)),
        ('奥古斯特', re.compile(r'奥古斯特', re.IGNORECASE)),
        ('Z23', re.compile(r'\bZ23\b', re.IGNORECASE)),
        ('长门', re.compile(r'长门', re.IGNORECASE)),
        ('标枪', re.compile(r'标枪', re.IGNORECASE)),
        ('狐娘', re.compile(r'天城|加贺|赤城|土佐|武藏', re.IGNORECASE)),
        ('柴郡', re.compile(r'柴郡', re.IGNORECASE)),
        ('爱宕', re.compile(r'爱宕', re.IGNORECASE)),
        ('德意志', re.compile(r'斯佩伯爵|德意志|希佩尔|俾斯麦', re.IGNORECASE)),
        ('塔什干', re.compile(r'塔什干', re.IGNORECASE)),
        ('企业', re.compile(r'企业', re.IGNORECASE)),
        ('黛朵', re.compile(r'黛朵', re.IGNORECASE)),
        ('大凤', re.compile(r'大凤', re.IGNORECASE)),
        ('誓约', re.compile(r'誓约', re.IGNORECASE)),
        ('信浓', re.compile(r'信浓', re.IGNORECASE)),
        ('光辉', re.compile(r'光辉', re.IGNORECASE)),
        ('天狼星', re.compile(r'天狼星', re.IGNORECASE)),
        ('贝尔法斯特', re.compile(r'贝尔法斯特', re.IGNORECASE)),
        ('恶毒', re.compile(r'恶毒', re.IGNORECASE)),
        ('绫波', re.compile(r'绫波', re.IGNORECASE)),
        ('能代', re.compile(r'能代|Noshiro', re.IGNORECASE)),
        ('拉菲', re.compile(r'拉菲', re.IGNORECASE)),
        ('可畏', re.compile(r'可畏', re.IGNORECASE)),
        ('欧根亲王', re.compile(r'欧根亲王', re.IGNORECASE)),
        ('独角兽', re.compile(r'独角兽', re.IGNORECASE)),
    ]),
    '明日方舟': OrderedDict([
        ('特蕾西娅', re.compile(r'特蕾西娅|Eterna', re.IGNORECASE)),
        ('温蒂', re.compile(r'温蒂|Weedy', re.IGNORECASE)),
        ('伊内丝', re.compile(r'伊内丝|Ines', re.IGNORECASE)),
        ('星熊', re.compile(r'星熊|Hoshiguma', re.IGNORECASE)),
        ('苇草', re.compile(r'苇草|Reed', re.IGNORECASE)),
        ('瑕光', re.compile(r'瑕光|Blemishine', re.IGNORECASE)),
        ('红', re.compile(r'\b红\b|^Red$', re.IGNORECASE)),
        ('絮雨', re.compile(r'絮雨|Whisperain', re.IGNORECASE)),
        ('白面鸮', re.compile(r'白面鸮|Ptilopsis', re.IGNORECASE)),
        ('普罗旺斯', re.compile(r'普罗旺斯|Provence', re.IGNORECASE)),
        ('特米米', re.compile(r'特米米|Tomimi', re.IGNORECASE)),
        ('澄闪', re.compile(r'澄闪|Goldenglow', re.IGNORECASE)),
        ('诗怀雅', re.compile(r'诗怀雅|Swire', re.IGNORECASE)),
        ('羽毛笔', re.compile(r'羽毛笔|Pluma', re.IGNORECASE)),
        ('塞雷娅', re.compile(r'塞雷娅|Saria', re.IGNORECASE)),
        ('麦哲伦', re.compile(r'麦哲伦|Magallan', re.IGNORECASE)),
        ('星极', re.compile(r'星极|Astesia', re.IGNORECASE)),
        ('安洁莉娜', re.compile(r'安洁莉娜|Angelina', re.IGNORECASE)),
        ('煌', re.compile(r'\b煌\b|Blaze', re.IGNORECASE)),
        ('迷迭香', re.compile(r'迷迭香|Rosmontis', re.IGNORECASE)),
        ('推进之王', re.compile(r'推进之王|Siege', re.IGNORECASE)),
        ('白金', re.compile(r'白金|Platinum', re.IGNORECASE)),
        ('森蚺', re.compile(r'森蚺|Eunectes', re.IGNORECASE)),
        ('霜叶', re.compile(r'霜叶|Frostleaf', re.IGNORECASE)),
        ('艾雅法拉', re.compile(r'艾雅法拉|Eyjafjalla', re.IGNORECASE)),
        ('蓝毒', re.compile(r'蓝毒|Blue.*?Poison', re.IGNORECASE)),
        ('闪灵', re.compile(r'闪灵|Shining', re.IGNORECASE)),
        ('林雨霞', re.compile(r'林雨霞|^Lin$', re.IGNORECASE)),
        ('霜星', re.compile(r'霜星|Frost.*?Nova', re.IGNORECASE)),
        ('幽灵鲨', re.compile(r'幽灵鲨|Specter', re.IGNORECASE)),
        ('斯卡蒂', re.compile(r'斯卡蒂|Skadi|Scadi', re.IGNORECASE)),
        ('黑', re.compile(r'\b黑\b|Schwarz', re.IGNORECASE)),
        ('铃兰', re.compile(r'铃兰|Suzuran', re.IGNORECASE)),
        ('能天使', re.compile(r'能天使|莫斯提马|蕾缪安|Mostima|Exusiai', re.IGNORECASE)),
        ('史尔特尔', re.compile(r'史尔特尔|Surtr', re.IGNORECASE)),
        ('龙娘', re.compile(r'\b[令黍年夕]\b', re.IGNORECASE)),
        ('W', re.compile(r'\bW\b', re.IGNORECASE)),
        ('泥岩', re.compile(r'泥岩|Mudrock', re.IGNORECASE)),
        ('陈', re.compile(r'\b陈\b|\bChen\b', re.IGNORECASE)),
        ('凯尔希', re.compile(r'凯尔希|Kaelis', re.IGNORECASE)),
        ('阿米娅', re.compile(r'阿米娅|Amiya', re.IGNORECASE)),
        ('德克萨斯', re.compile(r'德克萨斯|拉普兰德|Texas|Lappland', re.IGNORECASE)),
    ]),
    '星穹铁道': OrderedDict([
        ('爱莉希雅', re.compile(r'爱莉希雅|Elysia', re.IGNORECASE)),
        ('桂乃芬', re.compile(r'桂乃芬|Guinaifen', re.IGNORECASE)),
        ('遐蝶', re.compile(r'遐蝶|Castorice', re.IGNORECASE)),
        ('佩拉', re.compile(r'佩拉|佩拉格娅', re.IGNORECASE)),
        ('白露', re.compile(r'白露|Bailu', re.IGNORECASE)),
        ('乱破', re.compile(r'乱破|Rappa', re.IGNORECASE)),
        ('素裳', re.compile(r'素裳|Sushang', re.IGNORECASE)),
        ('星期日', re.compile(r'星期日|Sunday', re.IGNORECASE)),
        ('翡翠', re.compile(r'翡翠|ジェイド|Jade', re.IGNORECASE)),
        ('藿藿', re.compile(r'藿藿|Fofo', re.IGNORECASE)),
        ('穹', re.compile(r'\b穹\b|Caelus', re.IGNORECASE)),
        ('刃', re.compile(r'\b刃\b|Blade', re.IGNORECASE)),
        ('砂金', re.compile(r'砂金|Aventurine', re.IGNORECASE)),
        ('希儿', re.compile(r'希儿|Seele', re.IGNORECASE)),
        ('克拉拉', re.compile(r'克拉拉|Klara', re.IGNORECASE)),
        ('姬子', re.compile(r'姬子|Himeko', re.IGNORECASE)),
        ('青雀', re.compile(r'青雀|Qingque', re.IGNORECASE)),
        ('丹恒', re.compile(r'丹恒|饮月|Dan.*?Heng|Imbibitor.*?Lunae', re.IGNORECASE)),
        ('艾丝妲', re.compile(r'艾丝妲|Asta', re.IGNORECASE)),
        ('星核猎手', re.compile(r'星核猎手|萨姆|Sam', re.IGNORECASE)),
        ('云璃', re.compile(r'云璃|Yunli', re.IGNORECASE)),
        ('托帕', re.compile(r'托帕|Topaz', re.IGNORECASE)),
        ('飞霄', re.compile(r'飞霄|Feixiao', re.IGNORECASE)),
        ('灵砂', re.compile(r'灵砂|霊砂|Lingsha', re.IGNORECASE)),
        ('黑天鹅', re.compile(r'黑天鹅|Black.*?Swan', re.IGNORECASE)),
        ('布洛妮娅', re.compile(r'布洛妮娅|Bronya', re.IGNORECASE)),
        ('停云', re.compile(r'停云|忘归人|Tingyun|Fugue', re.IGNORECASE)),
        ('阮梅', re.compile(r'阮梅|ルアン.*?メェイ|Ruan.*?Mei', re.IGNORECASE)),
        ('镜流', re.compile(r'镜流|Jingliu', re.IGNORECASE)),
        ('黑塔', re.compile(r'黑塔|Herta', re.IGNORECASE)),
        ('开拓者', re.compile(r'开拓者|Trailblazer', re.IGNORECASE)),
        ('符玄', re.compile(r'符玄|FuXuan', re.IGNORECASE)),
        ('三月七', re.compile(r'三月七|March7th', re.IGNORECASE)),
        ('银狼', re.compile(r'银狼|SilverWolf', re.IGNORECASE)),
        ('知更鸟', re.compile(r'知更鸟|罗宾|Robin', re.IGNORECASE)),
        ('黄泉', re.compile(r'黄泉|芽衣|Acheron', re.IGNORECASE)),
        ('星', re.compile(r'\b星\b|Stelle', re.IGNORECASE)),
        ('卡芙卡', re.compile(r'卡芙卡|卡夫卡|Kafka', re.IGNORECASE)),
        ('花火', re.compile(r'花火|焰火|Sparkle', re.IGNORECASE)),
        ('萤', re.compile(r'\b萤\b|流萤|AR-26710|Firefly', re.IGNORECASE)),
    ]),
    '虚拟主播': OrderedDict([
        ('叶加濑冬雪', re.compile(r'叶加濑冬雪|葉加瀬冬雪|\bHakase|Fuyuki\b', re.IGNORECASE)),
        ('月之美兔', re.compile(r'月之美兔|月ノ美兎|\bTsukino|Mito\b', re.IGNORECASE)),
        ('夏色祭', re.compile(r'夏色祭|夏色まつり|\bNatsuiro|Matsuri\b', re.IGNORECASE)),
        ('结城昨奈', re.compile(r'结城昨奈|結城さくな|\bYuuki|Sakuna\b', re.IGNORECASE)),
        ('一条莉莉华', re.compile(r'一条莉|莉々華|\bIchijou|Ririka\b', re.IGNORECASE)),
        ('花芽堇', re.compile(r'花芽堇|花芽すみれ|\bSumire|Kaga\b', re.IGNORECASE)),
        ('星川莎拉', re.compile(r'星川莎拉|星川サラ|\bSara|Hoshikawa\b', re.IGNORECASE)),
        ('花谱', re.compile(r'花谱', re.IGNORECASE)),
        ('椎名唯华', re.compile(r'椎名唯华|椎名唯華|\bShiina|Yuika\b', re.IGNORECASE)),
        ('紫宫露娜', re.compile(r'紫宫露娜|紫宫るな|\bRuna|Shinomiya\b', re.IGNORECASE)),
        ('莉莉姆', re.compile(r'莉莉姆|噫噫姆|りりむ|\bRirimu|Makaino\b', re.IGNORECASE)),
        ('时雨羽衣', re.compile(r'时雨羽衣|时雨忧|しぐれうい|\bShigure\b', re.IGNORECASE)),
        ('莉泽·赫露艾斯塔', re.compile(r'莉泽·赫露艾斯塔|リゼ・ヘルエスタ|\bLize|Helesta\b', re.IGNORECASE)),
        ('塞莱希·法娜', re.compile(r'塞莱希·法娜|セレス|ファウナ|Ceres.*?Fauna', re.IGNORECASE)),
        ('角卷绵芽', re.compile(r'角卷绵芽|わため|\bTsunomaki|Watame\b', re.IGNORECASE)),
        ('绊爱', re.compile(r'绊爱|キズナアイ|\bKizuna\b', re.IGNORECASE)),
        ('桃铃音音', re.compile(r'桃铃音音|ねね|\bMomosuzu|Nene\b', re.IGNORECASE)),
        ('七诗无铭', re.compile(r'七诗无铭|ムメイ|\bNanashi|Mumei\b', re.IGNORECASE)),
        ('AZKi', re.compile(r'AZKi|Diva\b', re.IGNORECASE)),
        ('尾丸波尔卡', re.compile(r'尾丸波尔卡|ポルカ|\bOmaru|Polka\b', re.IGNORECASE)),
        ('神乐七奈', re.compile(r'神乐七奈|神楽|\bKagura|Nana\b', re.IGNORECASE)),
        ('华生·阿米莉亚', re.compile(r'华生·阿米莉亚|\bWatsonAmelia\b', re.IGNORECASE)),
        ('戌神沁音', re.compile(r'戌神沁音|ころね|\bInugami|Korone\b', re.IGNORECASE)),
        ('森美声', re.compile(r'森美声|カリオペ|\bMori|Calliope\b', re.IGNORECASE)),
        (
            '阿比斯加德',
            re.compile(r'阿比斯加德|アビスガード|\bFuwawa|Mococo|Abyssgard|FUWAMOCO\b', re.IGNORECASE),
        ),
        ('大空昴', re.compile(r'大空昴|スバル|\bOozora|Subaru\b', re.IGNORECASE)),
        ('大神澪', re.compile(r'大神澪|ミオ|\bOokami|Mio\b', re.IGNORECASE)),
        ('拉普拉斯', re.compile(r'拉普拉斯|达克尼斯|\bLaplus\b', re.IGNORECASE)),
        ('博衣小夜璃', re.compile(r'博衣小夜璃|こより|\bHakui|Koyori\b', re.IGNORECASE)),
        ('风真伊吕波', re.compile(r'风真伊吕波|いろは|\bKazama|Iroha\b', re.IGNORECASE)),
        ('一伊那尔栖', re.compile(r'一伊那尔栖|\bNinomae\b|いなにす', re.IGNORECASE)),
        ('白银诺艾尔', re.compile(r'白银诺艾尔|\bNoel|Shirogane\b', re.IGNORECASE)),
        ('紫咲诗音', re.compile(r'紫咲诗音|シオン|\bMurasaki|Shion\b', re.IGNORECASE)),
        ('猫又小粥', re.compile(r'猫又小粥|おかゆ|\bNekomata|Okayu\b', re.IGNORECASE)),
        ('樱巫女', re.compile(r'樱巫女|\bSakura|Miko\b', re.IGNORECASE)),
        ('狮白牡丹', re.compile(r'狮白牡丹|ぼたん|\bShishiro|Botan\b', re.IGNORECASE)),
        ('兔田佩克拉', re.compile(r'兔田佩克拉|ぺこら|\bUsada|Pekora\b', re.IGNORECASE)),
        ('天音彼方', re.compile(r'天音彼方|天音かなた|\bAmane|Kanata\b', re.IGNORECASE)),
        ('宝钟玛琳', re.compile(r'宝钟玛琳|\bHoushou|Marine\b|マリン', re.IGNORECASE)),
        ('白上吹雪', re.compile(r'白上吹雪|白上フブキ|\bShirakami|Fubuki\b', re.IGNORECASE)),
        ('沙花叉', re.compile(r'沙花叉|クロヱ|\bSakamata|Chloe|Orca\b', re.IGNORECASE)),
        ('噶呜·古拉', re.compile(r'噶呜·古拉|\bGawr|Gura|gawrt\b|がうる', re.IGNORECASE)),
        ('星街', re.compile(r'星街|彗星|\bSuisei|Hoshimachi\b|すいせい', re.IGNORECASE)),
        ('百鬼绫目', re.compile(r'百鬼绫目|百鬼あやめ|\bNakiri|Ayame\b', re.IGNORECASE)),
        ('润羽露西娅', re.compile(r'润羽露西娅|潤羽るしあ|\bUruha|Rushia\b', re.IGNORECASE)),
        ('雪花菈米', re.compile(r'雪花菈米|雪花ラミィ|\bYukihana|Lamy\b', re.IGNORECASE)),
        ('常暗永远', re.compile(r'常暗永远|\bTokoyami|Towa|TOWART\b|常闇', re.IGNORECASE)),
        ('凑阿库娅', re.compile(r'凑阿库娅|\bMinato|Aqua\b|湊あくあ', re.IGNORECASE)),
    ]),
    '蔚蓝档案': OrderedDict([
        ('阿慈谷日富美', re.compile(r'Ajitani\b|Hifumi|阿慈', re.IGNORECASE)),
        ('阿洛娜x普拉娜', re.compile(r'阿洛娜|普拉娜|Plana|Arrona|Alona', re.IGNORECASE)),
        ('白洲梓', re.compile(r'白洲梓|Shirasu.*?Azusa|白洲アズサ', re.IGNORECASE)),
        ('百合园圣亚', re.compile(r'Seia.*?Yurizono|百合园圣亚', re.IGNORECASE)),
        ('才羽姊妹', re.compile(r'才羽|绿×桃井|Saiba.*?(Momoi|Midori)', re.IGNORECASE)),
        ('秤亚津子', re.compile(r'秤亚津子|Atsuko', re.IGNORECASE)),
        ('春日梓', re.compile(r'春日|Kasuga.*?Tsubaki', re.IGNORECASE)),
        ('春原姐妹', re.compile(r'春原(心菜|旬)|Sunohara|Kokona|Shun', re.IGNORECASE)),
        ('大久保瑠美', re.compile(r'大久保瑠美|Asagi.*?Mutsuki', re.IGNORECASE)),
        ('丹花伊吹', re.compile(r'丹花|Tanga.*?Ibuki', re.IGNORECASE)),
        ('调月莉音', re.compile(r'调月莉音|\bRio\b', re.IGNORECASE)),
        ('锭前纱织', re.compile(r'锭前纱织|Joumae.*?Saori', re.IGNORECASE)),
        ('飞鸟马时', re.compile(r'飞鸟马时|Asuma.*?Toki', re.IGNORECASE)),
        ('各务千寻', re.compile(r'各务千寻|Chihiro', re.IGNORECASE)),
        ('古关忧', re.compile(r'古关忧|Kozeki', re.IGNORECASE)),
        ('鬼方佳代子', re.compile(r'鬼方佳代子|鬼方|佳代子|佳世子|Onikata.*?Kayoko', re.IGNORECASE)),
        ('鬼怒川霞', re.compile(r'鬼怒川霞|Kinugawa.*?Kasumi', re.IGNORECASE)),
        ('合欢垣吹雪', re.compile(r'合欢垣吹雪|合歓垣|Nakatsukasa', re.IGNORECASE)),
        ('和乐知世', re.compile(r'和乐知世|Waraku.*?Chise', re.IGNORECASE)),
        ('和泉元英美', re.compile(r'和泉元英美|Izumimoto.*?Eimi', re.IGNORECASE)),
        ('黑馆晴奈', re.compile(r'黑馆晴奈|Kurotate|Haruna', re.IGNORECASE)),
        ('黑见芹香', re.compile(r'黑见|黒見セリカ|Kuromi.*?Serika', re.IGNORECASE)),
        ('狐坂若藻', re.compile(r'狐坂|kosaka|Wakamo', re.IGNORECASE)),
        ('花冈柚子', re.compile(r'花冈|Hanaoka.*?Yuzu', re.IGNORECASE)),
        ('火宫千夏', re.compile(r'火宫千夏|Sorasaki.*?Hina|火宫チナツ', re.IGNORECASE)),
        ('间宵时雨', re.compile(r'间宵时雨|Mayoi.*?Shigure', re.IGNORECASE)),
        ('箭吹棕榈', re.compile(r'箭吹|Yabuki.*?Shuro', re.IGNORECASE)),
        ('久田伊树菜', re.compile(r'久田|Kuda.*?Izuna', re.IGNORECASE)),
        ('橘双子', re.compile(r'橘|Tachibana', re.IGNORECASE)),
        ('卡林', re.compile(r'卡林|Kakudate.*?Karin', re.IGNORECASE)),
        ('空井咲', re.compile(r'空井咲|Sorai.*?Saki', re.IGNORECASE)),
        ('栗村爱理', re.compile(r'栗村|Kurimura|Airi', re.IGNORECASE)),
        ('龙华妃咲', re.compile(r'龙华妃咲|Kisaki', re.IGNORECASE)),
        ('陆八魔亚瑠', re.compile(r'陆八魔亚瑠|Rikuhachima.*?Aru', re.IGNORECASE)),
        ('猫冢响', re.compile(r'猫冢响|Nekozuka.*?Hibiki', re.IGNORECASE)),
        ('明星日鞠', re.compile(r'明星日鞠|Himari', re.IGNORECASE)),
        ('浦和花子', re.compile(r'浦和花子|Urawa.*?Hanako|浦和ハナコ', re.IGNORECASE)),
        ('砂狼白子', re.compile(r'砂狼白子|Sunaookami.*?Shiroko|Shiroko.*?Terror', re.IGNORECASE)),
        ('生盐诺亚', re.compile(r'生盐|\bNoa\b', re.IGNORECASE)),
        ('圣园弥香', re.compile(r'圣园弥香|Mika.*?Misono', re.IGNORECASE)),
        ('十六夜野乃美', re.compile(r'十六夜野乃美|Izayoi.*?Nonomi', re.IGNORECASE)),
        ('天童爱丽丝', re.compile(r'天童爱丽丝|Alice.*?Tendou|天童アリス', re.IGNORECASE)),
        ('天雨亚子', re.compile(r'天雨亚子|Ako.*?Amau', re.IGNORECASE)),
        ('桐生桔梗', re.compile(r'桐生桔梗|Kiryuu.*?Kikyou', re.IGNORECASE)),
        ('桐藤渚', re.compile(r'桐藤渚|Nagisa.*?Kirifuji', re.IGNORECASE)),
        ('尾刃', re.compile(r'尾刃|Kanna.*?Ogata', re.IGNORECASE)),
        ('霞泽美游', re.compile(r'霞沢美游|霞泽美游|垃圾兔|Miyu', re.IGNORECASE)),
        ('下江小春', re.compile(r'下江小春|下江コハル', re.IGNORECASE)),
        ('小鸟游星野', re.compile(r'小鸟游星野|Takanashi.*?Hoshino|小鳥遊ホシノ', re.IGNORECASE)),
        ('小涂真纪', re.compile(r'小涂真纪|Maki.*?Konuri', re.IGNORECASE)),
        ('笑面教授', re.compile(r'笑面教授|Niyaniya', re.IGNORECASE)),
        ('杏山和纱', re.compile(r'杏山和纱|Kazusa', re.IGNORECASE)),
        ('夜樱绮良', re.compile(r'夜[樱桜]|绮良|Kirara', re.IGNORECASE)),
        ('一之濑明日奈', re.compile(r'一之濑明日奈|亚丝娜|Asuna|一之瀬.*?アスナ', re.IGNORECASE)),
        ('伊落玛丽', re.compile(r'伊落玛丽|Iochi', re.IGNORECASE)),
        ('伊藤静', re.compile(r'伊藤静|Haruhara.*?Shun', re.IGNORECASE)),
        ('银镜伊织', re.compile(r'银镜(伊织|イオリ)|Shiromi.*?Iori', re.IGNORECASE)),
        ('羽川莲实', re.compile(r'羽川莲实|Hanekawa.*?Hasumi', re.IGNORECASE)),
        ('羽沼真琴', re.compile(r'羽沼真琴|Makoto.*?Hanuma|羽沼.*?マコト', re.IGNORECASE)),
        ('月华梦骚', re.compile(r'月华梦|Marina.*?Ikekura', re.IGNORECASE)),
        ('月雪宫子', re.compile(r'月雪宫子|\bMiyako\b', re.IGNORECASE)),
        ('早濑优香', re.compile(r'早濑优香|Hayase.*?Yuuka', re.IGNORECASE)),
        ('枣伊吕波', re.compile(r'枣伊吕波|Natsume.*?Iroha', re.IGNORECASE)),
        ('一花x暴徒', re.compile(r'仲正一花|委员会のモブ|\bKiryuu Kikyou|Nakamasa Ichika\b', re.IGNORECASE)),
    ]),
    '原神': OrderedDict([
        ('卡维', re.compile(r'卡维|Kaveh', re.IGNORECASE)),
        ('赛诺', re.compile(r'赛诺|Cyno', re.IGNORECASE)),
        ('迪奥娜', re.compile(r'迪奥娜|Diona', re.IGNORECASE)),
        ('莱欧斯利', re.compile(r'莱欧斯利|Wriothesley', re.IGNORECASE)),
        ('艾尔海森', re.compile(r'艾尔海森|Alhaitham', re.IGNORECASE)),
        ('莱依拉', re.compile(r'莱依拉|Layla', re.IGNORECASE)),
        ('砂糖', re.compile(r'砂糖|Sucrose', re.IGNORECASE)),
        ('坎蒂丝', re.compile(r'坎蒂丝|Candace', re.IGNORECASE)),
        ('夏洛蒂', re.compile(r'夏洛蒂|Charlotte', re.IGNORECASE)),
        ('阿贝多', re.compile(r'阿贝多|Albedo', re.IGNORECASE)),
        ('早柚', re.compile(r'早柚|Sayu', re.IGNORECASE)),
        ('北斗', re.compile(r'北斗|Beidou', re.IGNORECASE)),
        ('迪希雅', re.compile(r'迪希雅|Dehya', re.IGNORECASE)),
        ('流浪者', re.compile(r'流浪者|放浪者', re.IGNORECASE)),
        ('希格雯', re.compile(r'希格雯|Sigewinne', re.IGNORECASE)),
        ('安柏', re.compile(r'安柏|Amber', re.IGNORECASE)),
        ('空', re.compile(r'\b空\b|Aether', re.IGNORECASE)),
        ('温迪', re.compile(r'温迪|Venti|巴巴托斯|Barbatos', re.IGNORECASE)),
        ('希诺宁', re.compile(r'希诺宁|Xilonen', re.IGNORECASE)),
        ('派蒙', re.compile(r'派蒙|Paimon', re.IGNORECASE)),
        ('迪卢克', re.compile(r'迪卢克|Diluc', re.IGNORECASE)),
        ('瑶瑶', re.compile(r'瑶瑶|Yaoyao', re.IGNORECASE)),
        ('柯莱', re.compile(r'柯莱|Collei', re.IGNORECASE)),
        ('琴', re.compile(r'\b琴\b|\bJean\b', re.IGNORECASE)),
        ('归终', re.compile(r'[归帰]终|guizhong', re.IGNORECASE)),
        ('千织', re.compile(r'千织|Chiori', re.IGNORECASE)),
        ('阿蕾奇诺', re.compile(r'阿蕾奇诺|Arlecchino|佩露薇利|Peruere', re.IGNORECASE)),
        ('久岐忍', re.compile(r'久岐忍|Kuki.*?Shinobu', re.IGNORECASE)),
        ('闲云', re.compile(r'闲云|XianYun', re.IGNORECASE)),
        ('那维莱特', re.compile(r'那维莱特|Neuvillette', re.IGNORECASE)),
        ('香菱', re.compile(r'香菱|Xiangling', re.IGNORECASE)),
        ('枫原万叶', re.compile(r'枫原万叶|Kaedehara.*?Kazuha', re.IGNORECASE)),
        ('九条裟罗', re.compile(r'九条裟罗|Kujou.*?Sara', re.IGNORECASE)),
        ('玛拉妮', re.compile(r'玛拉妮|Mualani', re.IGNORECASE)),
        ('烟绯', re.compile(r'烟绯|Yanfei', re.IGNORECASE)),
        ('琳妮特', re.compile(r'琳妮特|Lynette', re.IGNORECASE)),
        ('茜特菈莉', re.compile(r'茜特菈莉|Citlali', re.IGNORECASE)),
        ('诺艾尔', re.compile(r'诺艾尔|Noelle', re.IGNORECASE)),
        ('七七', re.compile(r'七七|Qiqi', re.IGNORECASE)),
        ('云堇', re.compile(r'云堇|云菫|YunJin', re.IGNORECASE)),
        ('可莉', re.compile(r'可莉|Klee', re.IGNORECASE)),
        ('珐露珊', re.compile(r'珐露珊|Faruzan', re.IGNORECASE)),
        ('娜维娅', re.compile(r'娜维娅|Naveah', re.IGNORECASE)),
        ('克洛琳德', re.compile(r'克洛琳德|Clorinde', re.IGNORECASE)),
        ('芭芭拉', re.compile(r'芭芭拉|Barbara', re.IGNORECASE)),
        ('菲谢尔', re.compile(r'菲谢尔|Fischl', re.IGNORECASE)),
        ('绮良々', re.compile(r'绮良[々良]|Kira', re.IGNORECASE)),
        ('凝光', re.compile(r'凝光|Ningguang', re.IGNORECASE)),
        ('达达利亚', re.compile(r'达达利亚|Tartaglia', re.IGNORECASE)),
        ('魈', re.compile(r'\b魈\b|Xiao', re.IGNORECASE)),
        ('莫娜', re.compile(r'莫娜|Mona', re.IGNORECASE)),
        ('优菈', re.compile(r'优菈|Eula', re.IGNORECASE)),
        ('夜兰', re.compile(r'夜兰|Yelan', re.IGNORECASE)),
        ('钟离', re.compile(r'钟离|Morax|岩[王神]|若陀龙王', re.IGNORECASE)),
        ('荧', re.compile(r'\b[荧萤蛍]\b|Lumine', re.IGNORECASE)),
        ('宵宫', re.compile(r'宵宫|Yoimiya', re.IGNORECASE)),
        ('心海', re.compile(r'心海|Sangonomiya.*?Kokomi', re.IGNORECASE)),
        ('纳西妲', re.compile(r'纳西妲|Nahida|草王|草神|大慈树王', re.IGNORECASE)),
        ('妮露', re.compile(r'妮露|Nilou', re.IGNORECASE)),
        ('胡桃', re.compile(r'胡桃|HuTao', re.IGNORECASE)),
        ('申鹤', re.compile(r'申鹤|Shenhe', re.IGNORECASE)),
        ('八重神子', re.compile(r'八重神子|Yae.*?Miko', re.IGNORECASE)),
        ('甘雨', re.compile(r'甘雨|Ganyu', re.IGNORECASE)),
        ('刻晴', re.compile(r'刻晴|Keqing', re.IGNORECASE)),
        ('神里绫华', re.compile(r'神里绫华|Kamisato.*?Ayaka', re.IGNORECASE)),
        ('芙宁娜', re.compile(r'芙宁娜|芙卡洛斯|Furina', re.IGNORECASE)),
        ('雷电将军', re.compile(r'雷电将军|雷神|雷神巴尔|雷电影', re.IGNORECASE)),
    ]),
    '其他': Subjects,
}


def match_tag_category(name: str, trans: str, categories: Categories) -> list[str]:
    """根据 Tag 匹配分类"""
    matched_categories: list[str] = []
    name = replace_cn_punctuation(name)
    trans = replace_cn_punctuation(trans)
    rname = readable_tag(name, trans)
    for category, pattern in categories.items():
        if pattern.search(name) or pattern.search(trans) or pattern.search(rname):
            matched_categories.append(category)
    return matched_categories


def match_major_categories(tags: list[dict[str, str | None]]) -> list[str]:
    """根据 Tag 匹配分类"""
    category_counter: Counter[str] = Counter()
    for tag in tags:
        name, trans = tag['name'] or '', tag.get('translated_name') or ''
        category_counter.update(match_tag_category(name, trans, MajorCategories))
    sorted_categories = category_counter.most_common()

    # 对于出现次数相同的类别，通过子类匹配进行进一步排序
    if len(sorted_categories) > 1 and sorted_categories[0][1] == sorted_categories[1][1]:
        first_category, first_num = sorted_categories[0]
        category_counter[first_category] += len(match_minor_categories(tags, first_category))

        for category, num in sorted_categories[1:]:
            if num == first_num:
                category_counter[category] += len(match_minor_categories(tags, category))

    return [category for category, _ in category_counter.most_common()]


def match_minor_categories(tags: list[dict[str, str | None]], category: str) -> list[str]:
    """根据 Tag 匹配子分类"""
    matched_categories: list[str] = []
    if minor_category := MinorCategories.get(category):
        for tag in tags:
            name, trans = tag['name'] or '', tag.get('translated_name') or ''
            matched_categories.extend(match_tag_category(name, trans, minor_category))
    return matched_categories


def has_minor_category(category: str) -> bool:
    """判断是否有子分类"""
    return category in MinorCategories


def match_any_minor_category(name, trans) -> bool:
    """根据 Tag 匹配任意子分类"""
    for minor in MinorCategories.keys():
        if match_tag_category(name, trans, MinorCategories[minor]):
            return True
    return False


ALBUM_TAGS = [
    '可爱',
    '萝莉',
    '风景',
    '百合',
    '触手',
    '异色瞳',
    '猫娘',
    '兽耳',
    '猫耳',
    '狐耳',
    '接吻',
    '婚纱',
    '肚脐',
    '鸭子坐',
    '好身材',
    '性感',
    '纯欲',
    '巨乳',
    '贫乳',
    '创可贴',
    'JK',
    '水手服',
    '体操服',
    'OL',
    '职业装',
    '女仆装',
    '护士',
    '旗袍',
    '泳衣',
    '泳装',
    '比基尼',
    '死库水',
    '洗澡',
    '内裤',
    '胖次',
    '屁股',
    '劈叉',
    '玉足',
    '美腿',
    '绝对领域',
    '白丝',
    '雪糕',
    '白裤袜',
    '黑丝',
    '巧克力',
    '黑裤袜',
]  # type: ignore
