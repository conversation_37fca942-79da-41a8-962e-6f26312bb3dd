'''程序逻辑异常'''


class LogicError(Exception):
    rc: int
    msg: str

    def __init__(self, msg: str | None = None):
        if msg is not None:
            self.msg = msg

    @property
    def name(self):
        return self.__class__.__name__

    def __str__(self):
        return f'<{self.name}: {self.rc}>'

    def to_dict(self):
        return {'rc': self.rc, 'msg': self.msg}

    @classmethod
    def create_subclass(cls, name, rc, msg):
        '''创建 LogicError 的子类'''
        return type(name, (cls,), {'rc': rc, 'msg': msg})


# 账号相关
EmailExists = LogicError.create_subclass('EmailExists', 1001, '邮箱已被注册')
VcodeError = LogicError.create_subclass('VcodeError', 1002, '验证码错误或已过期')
AccountBanned = LogicError.create_subclass('AccountBanned', 1003, '账号已被封禁')
EmailError = LogicError.create_subclass('EmailError', 1004, '邮箱错误')
PasswordError = LogicError.create_subclass('PasswordError', 1005, '密码错误')
CheckinRepeated = LogicError.create_subclass('CheckinRepeated', 1006, '今天已经打卡过了')
# 购买、权限相关
PermissionDenied = LogicError.create_subclass('PermissionDenied', 2001, '没有访问权限')
CoinError = LogicError.create_subclass('CoinError', 2002, '次元币错误')
NotSufficientCoins = LogicError.create_subclass('NotSufficientCoins', 2003, '次元币不足')
PurchaseDuplicate = LogicError.create_subclass('PurchaseDuplicate', 2004, '重复购买')
