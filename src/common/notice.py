import time
from pickle import HIGHEST_PROTOCOL, dumps, loads  # noqa: S403

from libs.cache import redis

__all__ = ['add_notice', 'del_notice', 'get_notice']


async def add_notice(notice: str, expire: int = 0) -> None:
    """添加公告"""
    expire_at = int(time.time()) + expire if expire else 0
    b_notice = dumps((notice, expire_at), HIGHEST_PROTOCOL)
    await redis.lpush('Notice', b_notice)  # type: ignore


async def get_notice() -> list[str]:
    """获取公告"""
    notices = []
    now = int(time.time())
    for b_notice in await redis.lrange('Notice', 0, -1):  # type: ignore
        notice, expire_at = loads(b_notice)  # noqa: S301
        if 0 < expire_at <= now:
            await redis.lrem('Notice', 0, b_notice)  # type: ignore
        else:
            notices.append(notice)
    return notices


async def del_notice(*notices: str) -> None:
    """删除公告"""
    now = int(time.time())
    for b_notice in await redis.lrange('Notice', 0, -1):  # type: ignore
        notice, expire_at = loads(b_notice)  # noqa: S301
        if 0 < expire_at <= now:
            await redis.lrem('Notice', 0, b_notice)  # type: ignore
        elif notice in notices:
            await redis.lrem('Notice', 0, b_notice)  # type: ignore


async def clear_notice() -> None:
    """清空公告"""
    await redis.delete('Notice')
