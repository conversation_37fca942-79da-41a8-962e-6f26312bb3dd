import logging
import sys
import traceback
from typing import <PERSON>Var

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Response
from pydantic import ValidationError
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from tortoise.exceptions import DoesNotExist

from apps import not_found
from apps.user.models import User
from common.errors import LogicError
from config import DEBUG, DOMAIN, MAX_SANITY, SESSION_MAX_AGE
from libs.http import JSONResponse, State, redirect, render
from libs.middleware import BaseMiddleware
from libs.session import Session

errlog = logging.getLogger('error')


class SessionMiddleware(BaseMiddleware):
    """会话中间件"""

    async def process_request(self, request: Request):
        # 获取session id
        sid = request.cookies.get('sid')
        request.scope['session'] = session = await Session.load(sid)
        session.update_last_time()

    async def process_response(self, request: Request, response: Response) -> Response:  # type: ignore
        session: Session = request.session  # type: ignore

        if sid := await session.save():
            response.set_cookie(
                key='sid',
                value=sid,
                max_age=SESSION_MAX_AGE,
                httponly=True,
                secure=not DEBUG,
                samesite='lax',
                domain=None if DEBUG else f'.{DOMAIN}',
            )
        elif session.is_empty and 'sid' in request.cookies:
            response.delete_cookie('sid', domain=None if DEBUG else f'.{DOMAIN}')  # 删除 Cookie 中的 sid
        return response


class AuthenticationMiddleware(BaseMiddleware):
    """认证中间件"""

    LOGIN_REQUIRED: ClassVar[list[str]] = [
        '/a/illust/download/',
        '/w/purchase/vip/',
        '/w/purchase/coin/',
        '/w/purchase/album/',  # RMB 购买
        '/w/album/purchase/',  # 次元币购买
        '/w/user/logout/',
        '/w/user/profile/',
        '/w/user/illusts/',
        '/w/user/albums/',
        '/w/user/followed/',
        '/a/user/follow/',
        '/a/user/unfollow/',
    ]

    def match_path(self, path: str) -> bool:
        for prefix in self.LOGIN_REQUIRED:
            if path.startswith(prefix):
                return True
        return False

    def set_max_sanity(self, request: Request):
        """获取最大的允许的涩图等级"""
        country = request.headers.get('cf-ipcountry')
        user = request.user
        if country and country != 'CN':
            max_sanity = MAX_SANITY['super-vip']
        elif not user:
            max_sanity = MAX_SANITY['anonymous']
        elif user.has_perms('fl_sanity'):
            match user.vip.level:
                case 1:
                    max_sanity = MAX_SANITY['low-vip']
                case 4 | 5:
                    max_sanity = MAX_SANITY['super-vip']
                case _:
                    max_sanity = MAX_SANITY['vip']
        else:
            max_sanity = MAX_SANITY['registered']
        State.set(max_sanity=max_sanity)

    async def process_request(self, request: Request):
        if uid := request.session.uid:  # type: ignore
            request.scope['auth'] = True
            request.scope['user'] = await User.get(id=uid)
            State.set(user=request.user)
            self.set_max_sanity(request)
            return
        else:
            request.scope['auth'] = False
            request.scope['user'] = None
            State.set(user=request.user)
            self.set_max_sanity(request)

            if self.match_path(request.url.path):
                return redirect('/w/user/login/?notice=true')


class UserAgentBlockMiddleware(BaseMiddleware):
    """UserAgent 拦截中间件"""

    # 拦截微信、QQ、支付宝等 App 内置浏览器的 UserAgent
    BLOCK_USER_AGENTS: ClassVar[list[str]] = [
        'MicroMessenger',  # 微信
        'QQ/',  # QQ
    ]

    async def process_request(self, request: Request):
        user_agent = request.headers.get('User-Agent', '')
        for blocked_pattern in self.BLOCK_USER_AGENTS:
            if blocked_pattern in user_agent:
                return render('apps/browserban.html', load_env=False)


class ExceptionMiddleware(BaseHTTPMiddleware):
    """异常处理中间件"""

    def print_local_traceback(self):
        """打印本地的异常堆栈"""
        exc_type, exc_value, exc_tb = sys.exc_info()
        if exc_type is None or exc_value is None or exc_tb is None:
            return
        tb_exc = traceback.TracebackException(exc_type, exc_value, exc_tb, capture_locals=True)

        for frame in tb_exc.stack.copy():
            if 'lib/python' in frame.filename or '/site-packages/' in frame.filename:
                tb_exc.stack.remove(frame)
        message = ''.join(tb_exc.format(chain=False))
        errlog.error(message)

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """处理异常"""
        try:
            return await call_next(request)
        except Exception:
            self.print_local_traceback()
            return Response(content='Internal Server Error', status_code=500)


def handle_logic_error(request: Request, exc: LogicError) -> Response:
    """处理业务逻辑错误"""
    return JSONResponse(content=exc, status_code=403)


def handle_validation_error(request: Request, exc: ValidationError) -> Response:
    """处理数据验证错误"""
    # 从验证错误中提取第一个错误信息
    error_msg = exc.errors()[0].get('msg', '数据验证失败')
    return JSONResponse(content={'rc': 1000, 'msg': error_msg}, status_code=403)


def handle_model_not_exist(request: Request, exc: DoesNotExist) -> Response:
    """处理模型不存在错误"""
    return not_found()


def handle_not_found(request: Request, exc: HTTPException) -> Response:
    """处理 404 错误"""
    return not_found()


def register_hooks(app: FastAPI):
    """注册钩子处理器"""

    # 注册异常钩子
    app.add_exception_handler(ValidationError, handle_validation_error)  # type: ignore
    app.add_exception_handler(LogicError, handle_logic_error)  # type: ignore
    app.add_exception_handler(DoesNotExist, handle_model_not_exist)  # type: ignore
    app.add_exception_handler(404, handle_not_found)  # type: ignore

    # 注册中间件
    middlewares = [
        ExceptionMiddleware,
        UserAgentBlockMiddleware,
        SessionMiddleware,
        AuthenticationMiddleware,
    ]
    middlewares.reverse()
    for middleware in middlewares:
        app.add_middleware(middleware)  # type: ignore
