#!/usr/bin/env python
import asyncio
import datetime
import json
import os
import re
import signal
import sys
import time
import warnings
from argparse import ArgumentParser
from collections import defaultdict
from functools import partial
from importlib import import_module
from pathlib import Path
from queue import Empty, Queue
from threading import Thread

import uvicorn
from IPython import embed
from IPython.core.getipython import get_ipython

from libs.terminal import print_dbg

# init database
BASEDIR = Path(__file__).absolute().parent
sys.path.append(BASEDIR.as_posix())


def register_stop_signals():
    """注册退出信号, 使其在接收到时停止事件循环"""

    def user_stop(signame, loop: asyncio.AbstractEventLoop):
        """用户中断程序时, 停止事件循环"""
        print(f'\n\n[{signame}] user canneled, exit.')
        loop.stop()
        time.sleep(1)
        sys.exit(0)

    loop = asyncio.get_running_loop()
    for signame in ('SIGINT', 'SIGQUIT', 'SIGTERM'):
        loop.add_signal_handler(getattr(signal, signame), partial(user_stop, signame, loop))


def set_env(env_name):
    """设置 生产/测试 环境"""
    env_name = env_name or os.environ.get('ALBUM_ENV', 'test')
    os.environ['ALBUM_ENV'] = env_name


def set_venv(env_name):
    """设置虚拟环境"""
    env_name = env_name or os.environ.setdefault('ALBUM_ENV', 'test')
    lines = [
        '\n# 设置 Album 环境变量\n',
        f'export ALBUM_ENV="{env_name}"\n',
        f'export ALBUM_BASE="{BASEDIR}"\n',
        f'export PATH="{BASEDIR}/tools:$PATH"\n',
        '# 将 manage.py 的子命令设置为别名\n',
        f'alias manage="python {BASEDIR}/manage.py"\n',
        f'alias shell="python {BASEDIR}/manage.py -e {env_name} shell"\n',
        f'alias run="python {BASEDIR}/manage.py -e {env_name} run"\n',
        'alias logf="sudo journalctl -u albumd -f"\n',
    ]

    # 获取虚拟环境路径
    if not (activate_path := Path(os.environ.get('VIRTUAL_ENV', ''), 'bin', 'activate')).exists():
        if not (activate_path := BASEDIR / '.venv' / 'bin' / 'activate').exists():
            if not (activate_path := BASEDIR.parent / '.venv' / 'bin' / 'activate').exists():
                print('未找到虚拟环境')
                sys.exit(1)

    # 向 activate 文件中追加 ALBUM_ENV 环境变量及命令别名
    with open(activate_path, 'r+') as fp:
        content = fp.read()
        idx = content.find(lines[0])  # 查找是否已经存在 ALBUM_ENV 环境变量
        if idx != -1:
            fp.seek(idx)
            fp.truncate()
        fp.write(''.join(lines))


async def init_db():
    """初始化数据库"""
    from tortoise import Tortoise

    from libs.orm import Model

    register_stop_signals()

    await Model.init_db()
    await Tortoise.generate_schemas(safe=True)  # 初始化 ORM


def shell():
    """启动交互式 shell"""
    from config import ALBUM_ENV, DEBUG
    from libs.http import State
    from libs.orm import Model
    from libs.terminal import blue, print_dbg, print_warn

    print_warn('Albumd Shell')
    print(f'Env: {blue(ALBUM_ENV)}  Debug: {blue(DEBUG)}\n')

    print_dbg('Initializing state and database...')
    token = State.init()
    State.set(max_sanity=6)
    asyncio.run(Model.init_db())

    # 导入调试时可能需要的模块
    print_dbg('Importing modules...')
    std_imports = ['asyncio', 'datetime', 'json', 'os', 'pathlib.Path', 'random', 're', 'sys', 'time']
    third_imports = ['PIL.Image', 'tortoise.expressions.F', 'tortoise.expressions.Q']
    album_imports = [
        'config cfg',
        'config.Vip',
        'config.Coin',
        'apps.album.models.*',
        'apps.payment.models.*',
        'apps.payment.signature.*',
        'apps.pixiv.models.*',
        'apps.user.models.*',
        'common.utils ut',
        'common.notice.*',
        'libs.cache.*',
        'libs.http',
        'libs.orm.Model',
        'libs.session.Session',
        'libs.static.*',
        'libs.terminal.*',
        'main.app',
    ]
    namespace = {}
    for name in std_imports + third_imports + album_imports:
        if ' ' in name:
            name, alias = name.split(' ')
            namespace[alias] = import_module(name)
        elif '.' in name:
            parent_name, import_name = name.rsplit('.', 1)
            module = import_module(parent_name)
            if import_name == '*':
                # 导入模块中的所有对象
                for import_name in module.__all__:
                    namespace[import_name] = getattr(module, import_name)
            else:
                try:
                    namespace[import_name] = getattr(module, import_name)
                except AttributeError:
                    namespace[import_name] = import_module(name)
        else:
            namespace[name] = import_module(name)

    # 启动 shell
    embed(using='asyncio', user_ns=namespace, colors='linux', enable_tip=False)

    try:
        # 安全退出
        warnings.filterwarnings('ignore', category=RuntimeWarning, message='coroutine .* was never awaited')
        asyncio.run(namespace['redis'].aclose())
        if ipy := get_ipython():
            ipy.exit()  # type: ignore
    except (RuntimeError, RuntimeWarning):
        sys.exit(0)
    finally:
        State.reset(token)


def run_server(bind='localhost:8000'):
    """启动服务器"""
    import config

    print('\x1b[1;32mAlbumd Launched\x1b[0m')
    print(f'\x1b[1;32mENV\x1b[0m: {config.ALBUM_ENV}\t\x1b[1;32mDEBUG\x1b[0m: {config.DEBUG}')

    host = port = uds = None
    if ':' in bind:
        host, port = bind.split(':')
        port = int(port)
    elif '/' in bind:
        uds = bind
    elif config.DEBUG:
        host = 'localhost'
        port = 8000
    else:
        uds = '/tmp/albumd.sock'  # noqa: S108
    uvicorn.run('main:app', host=host, port=port, uds=uds, **config.UVICORN)  # type: ignore


async def import_pixiv_data(import_type: str, json_files_and_dirs: list[Path], mode: str = 'update'):
    """导入 Pixiv 数据"""
    from apps.pixiv.models import Artist, Illust, Ranking, Tag
    from common.notice import add_notice
    from common.utils import cn_date, find_json_files
    from libs.cache import redis

    register_stop_signals()
    await Illust.init_db()

    # 选择要执行的操作
    if mode == 'append':
        if import_type == 'illust':
            exists = [str(iid) for iid in await Illust.all().values_list('id', flat=True)]
        elif import_type == 'artist':
            exists = [str(aid) for aid in await Artist.all().values_list('id', flat=True)]
        elif import_type == 'ranking':
            exists = {d.strftime('%Y%m%d') for d in await Ranking.all().values_list('date', flat=True)}  # type: ignore
        else:
            raise TypeError(f'Unknown import type: {import_type}')
    else:
        exists = []

    function = {
        'illust': Illust.import_from_json,
        'artist': Artist.import_from_json,
        'ranking': Ranking.import_from_json,
    }[import_type]

    # 查找目标 JSON 文件, 并导入
    seq = num = 0
    for json_path in find_json_files(json_files_and_dirs):
        if mode == 'append' and str(json_path.stem) in exists:
            continue
        else:
            seq += 1
            print(f'{seq:5d}. importing {json_path}')
            _, new = await function(json_path)
            num += new

    # 更新统计字段
    if import_type in ['illust', 'artist']:
        print('Updating statistics numbers ...')
        await Artist.update_all_illust_num()
        await Artist.update_all_latest_iid()
        await Tag.update_all_illust_num()

    # 重建 Model 缓存
    print('Rebuilding cache ...')
    await Illust.rebuild_cache()
    await Artist.rebuild_cache()
    await Tag.rebuild_cache()
    # 清除页面缓存
    await redis.del_pattern('Page::*')
    # 发布公告
    if import_type == 'illust' and num > 0:
        today = cn_date(datetime.date.today(), 'd')
        msg = f'📢【上新】{today}新增插画 {num} 张！'
        await add_notice(msg, 86400 * 7)
        print(msg)


async def import_album_data(json_files_and_dirs: list[Path]):
    """导入 Album 数据"""
    from apps.album.models import Album
    from common.notice import add_notice
    from common.utils import cn_date, find_json_files
    from libs.terminal import print_inf, print_note

    register_stop_signals()
    await Album.init_db()

    # 导入 Album 数据
    num = 0
    for json_path in find_json_files(json_files_and_dirs):
        print(f'Importing Album from "{json_path}"')
        with open(json_path, encoding='utf-8') as f:
            data = json.load(f)
        if isinstance(data, dict):
            data = [data]

        for album_data in data:
            alb, new = await Album.import_from_dict(album_data, 'title')
            if new:
                num += 1
                print_inf(f'Created {alb}: {alb.title}')
            else:
                print_note(f'Updated {alb}: {alb.title}')

    # 发布公告
    if num > 0:
        today = cn_date(datetime.date.today(), 'd')
        await add_notice(f'📣【上新】{today}新增专辑 {num} 个！', 86400 * 7)


async def import_extended_data(*json_paths: Path):
    """填充额外字段"""
    from apps.pixiv.models import Illust

    register_stop_signals()
    await Illust.init_db()

    for json_path in json_paths:
        if not json_path.exists():
            print(f'JsonFile not exists: {json_path}')
            continue

        with json_path.open('r', encoding='utf-8') as fp:
            ext_data = json.load(fp)
        ext_data = sorted(ext_data.items(), key=lambda x: int(x[0]))
        await Illust.import_extended_data(*ext_data)


def thumbnail(  # noqa: C901
    illust_paths: list[Path],
    small_dir: Path | None = None,
    large_dir: Path | None = None,
    overwrite: bool = False,
    interval: int = 0,
    n_thread: int = 4,
):
    """压缩插画为大图和缩略图"""
    import imagesize

    import common.utils as ut

    # 确保缩略图目录存在
    if small_dir:
        small_dir.mkdir(mode=0o755, parents=True, exist_ok=True)
    if large_dir:
        large_dir.mkdir(mode=0o755, parents=True, exist_ok=True)

    if not (small_dir or large_dir):
        print('No output directory specified.')
        sys.exit(0)

    # 搜索目录中的所有插画作品
    print('Searching illusts...')
    illusts: dict[int, list[Path]] = defaultdict(list)
    for ipath in ut.find_illusts(illust_paths):
        illusts[int(ipath.stem.split('_')[0])].append(ipath)

    trim = '\x1b[K'
    force_exit = False
    max_aspect = 16 / 9

    def compress_worker(input_q: Queue[tuple[int, int, list[Path]]], output_q: Queue[tuple[int, dict]]):
        """压缩图片的工作线程"""
        while not force_exit:
            try:
                n, iid, ipaths = input_q.get_nowait()
            except Empty:
                break

            print(f'{trim} {n}. Compressing illust {iid}')

            ipaths.sort(key=lambda p: p.stem)
            # 解析分辨率
            w, h = imagesize.get(ipaths[0])
            aspect = round(w / h, 3)
            # 记录图片名称
            imgs = {}
            imgs['origin'] = [ipath.name for ipath in ipaths]

            if small_dir:
                print(f'    making thumbnail for {iid}...', end=f'{trim}\r')
                imgs['thumb'] = ut.make_thumbnail(
                    ipaths[0], small_dir, 540, max_aspect=max_aspect, overwrite=overwrite, suffix='_540', strict=True
                ).name  # type: ignore
            if large_dir:
                print(f'    making large_img for {iid}...', end=f'{trim}\r')
                imgs['large'] = [
                    ut.make_thumbnail(p, large_dir, 1200, overwrite=overwrite, suffix='_1200', strict=False).name
                    for p in ipaths
                ]
            output_q.put((iid, {'w': w, 'h': h, 'aspect': aspect, 'imgs': imgs}))  # 将结果放入队列
            if interval:
                time.sleep(interval)

    record: dict[int, dict] = {}
    task_q: Queue[tuple[int, int, list[Path]]] = Queue()
    result_q: Queue[tuple[int, dict]] = Queue()
    workers = [Thread(target=compress_worker, args=(task_q, result_q)) for _ in range(n_thread)]

    for n, (iid, ipaths) in enumerate(illusts.items(), start=1):
        task_q.put((n, iid, ipaths))

    try:
        # 启动工作线程
        for worker in workers:
            worker.start()
        # 等待工作线程完成
        for worker in workers:
            worker.join()
        print(f'{trim} All workers done.')
    except KeyboardInterrupt:
        force_exit = True
        print(f'{trim}\nSaving and exiting...')

    while not result_q.empty():
        iid, item = result_q.get()
        record[iid] = item

    # 保存记录
    now = datetime.datetime.now().strftime('%y%m%d_%H%M')
    with open(f'ExtendData-{now}-{len(record)}.json', 'w') as fp:
        json.dump(record, fp, ensure_ascii=False, separators=(',', ':'), sort_keys=True)


async def handle_user(  # noqa: C901
    identities: list[str],
    info: bool = False,
    name: str | None = None,
    email: str | None = None,
    password: str | None = None,
    coin: int | None = None,
    vip: int | None = None,
    ban: bool | None = None,
    last: int | None = None,
    total: bool = False,
):
    """处理用户操作"""
    from tortoise.expressions import Q

    from apps.user.models import User
    from common.utils import iso_date
    from libs.session import Session
    from libs.terminal import print_err, print_inf, print_note, print_warn, yellow

    register_stop_signals()
    await User.init_db()
    email_pattern = re.compile(r'^[\w._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{1,}$')

    if last is not None:
        if activate_users := await Session.recent_active(days=last):
            print_note(f'最近 {last} 天活跃的用户：')
            for n, (uid, date) in enumerate(activate_users, start=1):  # type: ignore
                user = await User.get(id=uid)
                user_info = (
                    f'  {n:3d}. {user:12s}\t{user.vip}  '
                    f'{len(user.purchased["illusts"]):>4d} 🎨  '
                    f'{len(user.purchased["albums"]):>2d} 📒  '
                    f'{date}  {user.name}'
                )
                if user.vip.level >= 4:
                    print_warn(user_info)
                elif user.vip.level >= 1:
                    print_inf(user_info)
                else:
                    print_dbg(user_info, False)
        else:
            print_warn(f'最近 {last} 天没有活跃用户')
        return

    if 'all' in identities:
        users = User.all()
    else:
        ids, emails = [], []
        for ident in identities:
            if ident.isdecimal():
                ids.append(int(ident))
            elif email_pattern.match(ident):
                emails.append(ident)
            else:
                print_warn(f'Invalid ident: {ident}')

        users = User.filter(Q(id__in=ids) | Q(email__in=emails))

    count = await users.count()
    if total:
        print(f'Total users: {count:d}')
        return

    if count <= 0:
        print_err(f'No user found with ident: {identities}')
        return

    names = name.split(',') if name else []
    emails = email.split(',') if email else []

    for idx, user in enumerate(await users):
        if names:
            try:
                user.name = names[idx]
                await user.save(update_fields=['name'])
                print(f'Set {user} name as {yellow(name)}')
            except IndexError:
                print_warn(f'No more names to set for user {user.id}')
                break

        if emails:
            eml = emails[idx].strip()
            if not eml or not email_pattern.match(eml):
                print_warn(f'Invalid email format: {eml}')
                continue
            try:
                user.email = eml
                await user.save(update_fields=['email'])
                print(f'Set {user} email as {yellow(email)}')
            except Exception as e:
                print_err(f'{e} (Error setting {eml} for {user})')

        if password:
            await user.set_password(password)
            print(f'Set {user} password as {yellow(password, bold=True)}')

        if coin:
            print(f'Original: {user} coins {user.coins}')
            user.coins = coin
            await user.save()
            print_warn(f'Modified: {user} coins {user.coins}')

        if vip is not None:
            print(f'Original: {user} {user.vip} ({iso_date(user.vend) or "NaN"})')
            await user.set_vip(vip)
            print_warn(f'Modified: {user} {user.vip} ({iso_date(user.vend) or "NaN"})')

        if ban is not None:
            await (user.ban() if ban else user.unban())
            print_err(f'{user} {"banned" if ban else "unbanned"}')

        if info:
            print(
                f'用户 ID: {user.id}\n'
                f'> 邮箱：{user.email}\n> 昵称：{user.name}\n> 封禁：{user.banned}\n'
                f'> 金币：{user.coins}\n> 注册：{iso_date(user.created)}\n'
                f'> 会员：{user.vip} ({iso_date(user.vend) or "NaN"})\n'
                f'> 插画数：{len(user.purchased["illusts"])}\n> 专辑数：{len(user.purchased["albums"])}\n'
            )


async def delete_data(data_type: str, id_list: list[int]):
    """删除数据"""
    from apps.album.models import Album
    from apps.pixiv.models import Artist, Illust, Tag
    from apps.user.models import User
    from libs.terminal import print_err, yellow

    register_stop_signals()
    await User.init_db()
    objects = {
        'album': Album.filter(id__in=id_list),
        'artist': Artist.filter(id__in=id_list),
        'illust': Illust.filter(id__in=id_list),
        'tag': Tag.filter(id__in=id_list),
        'user': User.filter(id__in=id_list),
    }[data_type]

    if input(yellow(f'Are you sure to delete {len(id_list)} {data_type} (Y/n)? ', bold=True)) == 'Y':
        for obj in await objects:  # type: ignore
            print_err(f'Deleting {data_type} {obj.id}')
            await obj.delete()


async def handle_notice(
    new_notice: str | None,
    list_all: bool | None,
    delete: str | None,
    clear: bool | None,
    expire: int = 0,
):
    """处理通知"""
    from common.notice import add_notice, clear_notice, del_notice, get_notice
    from libs.terminal import print_err, print_inf, read_chars, yellow

    async def show_all() -> list[str]:
        if notices := await get_notice():
            for n, notice in enumerate(notices, start=1):
                print_inf(f'{n:2d}. {notice}')
        else:
            print_inf('暂无公告')
        return notices

    register_stop_signals()

    if new_notice:
        # 添加公告
        await add_notice(new_notice, expire)

    elif list_all:
        # 列出所有公告
        await show_all()

    elif delete:
        # 删除公告
        if all_notices := await show_all():
            seqs_input = input('请输入要删除的公告序号，多个序号用空格分隔：').split()
            seqs = [int(s) - 1 for s in seqs_input if s.isdecimal()]
            to_delete = [all_notices[idx] for idx in seqs if 0 <= idx < len(all_notices)]
            await del_notice(*to_delete)
    elif clear:
        # 清空公告
        if read_chars(yellow('确定要清空所有公告吗? (Y/n) '), 1).upper() == 'Y':
            await clear_notice()
            print_inf('所有公告已清空')
    else:
        print_err('Invalid command')


async def handle_list(n_order: int, hits=None):  # noqa: C901
    """列出数据"""
    from apps.payment.models import Order, Status
    from apps.pixiv.models import Artist, Illust, Tag
    from libs.cache import redis
    from libs.terminal import print_dbg, print_inf, yellow

    register_stop_signals()
    await Order.init_db()

    if n_order > 0:
        # 查看订单数据
        for order in await Order.all().order_by('-id').limit(n_order):
            if order.status == Status.paid:
                print_inf(f'{order}')
            else:
                print_dbg(f'{order}', False)
    if hits:
        # 查看统计数据
        pattern = 'Hit::*' if hits == 'all' else f'Hit::{hits.title()}*'
        for statistic_name in await redis.keys(pattern):
            show_name = statistic_name[5:]
            print_inf(show_name)
            for name, num in await redis.zrevrange(statistic_name, 0, 30, withscores=True, score_cast_func=int):
                if show_name == 'Artist' or show_name == 'Follow':
                    artist = await Artist.get_or_none(id=int(name))
                    name = artist.name if artist else yellow(f'aid={int(name)}')
                elif show_name == 'Illust':
                    illust = await Illust.get_or_none(id=int(name))
                    name = (await illust.artist()).name if illust else yellow(f'iid={int(name)}')
                elif show_name == 'Tag':
                    tag = await Tag.get_or_none(id=int(name))
                    name = tag.cname if tag else yellow(f'tid={int(name)}')
                else:
                    name = name.decode('utf8')

                if num > 1:
                    print(f'{num:6d} {name}')
            print()


if __name__ == '__main__':
    parser = ArgumentParser('manage', description='项目管理工具')
    parser.add_argument(
        '-e',
        '--env',
        choices=['prod', 'test', 'remote'],
        help='当前环境：prod 生产环境, test 测试环境, remote 远程环境',
    )

    subparsers = parser.add_subparsers(dest='command', help='子命令')

    # set-venv 子命令
    set_venv_parser = subparsers.add_parser('set-venv', help='创建虚拟环境')

    # init-db 子命令
    init_parser = subparsers.add_parser('init-db', help='初始化数据库')

    # shell 子命令
    shell_parser = subparsers.add_parser('shell', help='进入 IPython 终端环境进行调试')

    # run 子命令
    run_parser = subparsers.add_parser('run', help='启动项目')
    run_parser.add_argument('bind', nargs='?', type=str, default='localhost:8000', help='服务器主机地址及端口')

    # import 子命令
    import_parser = subparsers.add_parser('import', help='导入数据')
    import_parser.add_argument('-m', dest='mode', choices=['update', 'append'], default='append', help='导入模式')
    import_parser.add_argument(
        'type',
        choices=['illust', 'artist', 'ranking', 'album', 'extend'],
        help='导入数据的类型',
    )
    import_parser.add_argument('targets', type=Path, nargs='+', help='目标文件或文件夹')

    # thumbnail 子命令
    compress_parser = subparsers.add_parser('thumbnail', help='为插画创建大图和缩略图')
    compress_parser.add_argument('-s', dest='small_dir', type=Path, help='小图文件夹')
    compress_parser.add_argument('-l', dest='large_dir', type=Path, help='大图文件夹')
    compress_parser.add_argument('-f', dest='force_overwrite', action='store_true', help='强制覆盖已存在的文件')
    compress_parser.add_argument('-i', dest='interval', type=int, default=0, help='压缩间隔时间')
    compress_parser.add_argument('-t', dest='threads', type=int, default=1, help='工作线程数')
    compress_parser.add_argument('targets', type=Path, nargs='+', help='目标文件或文件夹')

    # user 子命令
    user_parser = subparsers.add_parser('user', help='用户相关操作')
    user_parser.add_argument('-i', dest='info', action='store_true', help='查看用户信息')
    user_parser.add_argument('-n', dest='name', type=str, help='修改用户昵称')
    user_parser.add_argument('-e', dest='email', type=str, help='修改用户 e-mail 地址')
    user_parser.add_argument('-p', dest='password', type=str, help='修改用户密码')
    user_parser.add_argument('-c', dest='coin', type=int, help='设置用户次元币数量')
    user_parser.add_argument('-v', dest='vip', type=int, help='设置用户 VIP 等级')
    user_parser.add_argument('-b', dest='ban', type=bool, help='封禁用户')
    user_parser.add_argument('-l', dest='last', type=int, metavar='DAYS', help='查看最近 N 天活跃的用户')
    user_parser.add_argument('-t', dest='total', action='store_true', help='统计总用户数')
    user_parser.add_argument('identities', type=str, nargs='*', help='用户 id 或 email，空格分隔多值，all 表示所有用户')

    # delete 子命令
    delete_parser = subparsers.add_parser('delete', help='删除数据')
    delete_parser.add_argument('type', choices=['illust', 'artist', 'album', 'tag', 'user'], help='删除数据的类型')
    delete_parser.add_argument('id_list', type=int, nargs='+', help='目标文件或文件夹')

    # notice 子命令
    notice_parser = subparsers.add_parser('notice', help='设置公告')
    notice_grp = notice_parser.add_mutually_exclusive_group(required=True)
    notice_grp.add_argument('-a', dest='add', type=str, help='设置公告')
    notice_grp.add_argument('-l', dest='list_all', action='store_true', help='列出公告')
    notice_grp.add_argument('-d', dest='delete', action='store_true', help='删除公告')
    notice_grp.add_argument('-c', dest='clear', action='store_true', help='清空所有公告')
    notice_parser.add_argument('-e', dest='expire', type=int, default=0, help='公告过期时间（秒）')

    # list 子命令
    list_parser = subparsers.add_parser('list', help='列出数据')
    list_parser.add_argument('-o', dest='order', type=int, default=0, help='按时间顺序列出订单')
    list_parser.add_argument('-t', dest='hits', type=str, help='列出浏览量统计')

    args = parser.parse_args()
    set_env(args.env)

    if args.command == 'set-venv':
        set_venv(args.env)
    elif args.command == 'init-db':
        asyncio.run(init_db())
    elif args.command == 'shell':
        shell()
    elif args.command == 'run':
        run_server(args.bind)
    elif args.command == 'import':
        if args.type in ['illust', 'artist', 'ranking']:
            asyncio.run(import_pixiv_data(args.type, args.targets, args.mode))
        elif args.type == 'extend':
            asyncio.run(import_extended_data(*args.targets))
        else:
            asyncio.run(import_album_data(args.targets))
    elif args.command == 'thumbnail':
        thumbnail(args.targets, args.small_dir, args.large_dir, args.force_overwrite, args.interval, args.threads)
    elif args.command == 'user':
        try:
            asyncio.run(
                handle_user(
                    args.identities,
                    info=args.info,
                    name=args.name,
                    email=args.email,
                    password=args.password,
                    coin=args.coin,
                    vip=args.vip,
                    ban=args.ban,
                    last=args.last,
                    total=args.total,
                )
            )
        except Exception as e:
            print(f'Unexpected error: {e}')
    elif args.command == 'delete':
        asyncio.run(delete_data(args.type, args.id_list))
    elif args.command == 'notice':
        asyncio.run(handle_notice(args.add, args.list_all, args.delete, args.clear, args.expire))
    elif args.command == 'list':
        asyncio.run(handle_list(args.order, args.hits))
    else:
        parser.print_help()
