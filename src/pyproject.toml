[project]
name = "album-srv"
version = "0.1.0"
description = "Album Server"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aerich==0.9.0",
    "asyncpg==0.30.0",
    "b2sdk==2.8.1",
    "cryptography==45.0.2",
    "fastapi[all]==0.115.12",
    "httpx==0.28.1",
    "ipython==9.2.0",
    "jinja2==3.1.6",
    "mailjet-rest==1.4.0",
    "pillow==11.2.1",
    "py7zr==0.22.0",
    "redis==6.1.0",
    "tortoise-orm==0.25.0",
    "user-agents>=2.2.0",
    "uvicorn==0.34.2",
    "xxhash==3.5.0",
    "zhconv==1.4.3",
]

[dependency-groups]
dev = [
    "djlint>=1.36.4",
    "filetype>=1.2.0",
    "imagesize>=1.4.1",
    "mypy>=1.15.0",
    "pickleshare>=0.7.5",
]

[tool.ruff]
line-length = 120
unsafe-fixes = false

[tool.ruff.format]
quote-style = "single"
