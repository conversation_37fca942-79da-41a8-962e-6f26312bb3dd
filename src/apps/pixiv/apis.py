import datetime

from tortoise.expressions import Q

import config as cfg
from apps.pixiv.models import Artist, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ranking, Tag
from apps.user.models import User
from libs.cache import redis
from libs.http import State


def get_max_sanity() -> int:
    """获取最大的允许的涩图等级"""
    return State.get('max_sanity', 2)


async def illust_rcmd(order: str = 'id', page: int = 1, aspect: str = 'all', ss: str = 'all', spos=0):
    """推荐接口"""
    page = max(page, 1)  # 页码最小为1
    offset = (page - 1) * cfg.PER_PAGE
    if order == 'default':
        order = 'added'

    conditions = []
    if aspect == 'ver':
        conditions.append(Q(aspect__lt=0.9))
    elif aspect == 'hor':
        conditions.append(Q(aspect__gt=1.1))
    elif aspect == 'sqr':
        conditions.append(Q(aspect__gte=0.9, aspect__lte=1.1))

    max_sanity = get_max_sanity()
    if ss == '6' and max_sanity >= 6:
        conditions.append(Q(sanity__gte=6))
    elif ss == '4' and max_sanity >= 4:
        conditions.append(Q(sanity=4))
    elif ss == '2':
        conditions.append(Q(sanity=2))
    else:
        conditions.append(Q(sanity__lte=max_sanity))

    illusts = await Illust.filter(*conditions).order_by(f'-{order}', '-id').limit(cfg.PER_PAGE).offset(offset)
    illusts, spos = IAligner(spos).align(illusts)
    return [await il.overview(with_artist=True) for il in illusts], spos


async def idetail(iid: int):
    """插画详情"""
    illust = await Illust.get(id=iid)
    return await illust.detail()


async def irelated(iid: int):
    """相关插画"""
    illust = await Illust.get(id=iid)
    illusts: list[Illust] = await illust.related(get_max_sanity(), cfg.PER_PAGE * 2)  # type: ignore
    illusts, spos = IAligner().align(illusts)
    return [await il.overview(with_artist=True) for il in illusts], spos


async def ranking(dt: str | None = None, page: int = 1):
    """排行接口"""
    # 取出参数
    page = max(page, 1)
    offset = (page - 1) * cfg.PER_PAGE
    if dt is None:
        date = datetime.date.today() - datetime.timedelta(1)
    else:
        date = datetime.date.fromisoformat(dt)

    # 从 cache 或 db 中获取
    cache_key = f'Ranking::{date}-{page}'
    illusts: list[Illust] = await redis.get(cache_key)  # type: ignore
    if not illusts:
        illusts = await Ranking.get_ranking(date, cfg.PER_PAGE, offset)  # type: ignore
        await redis.set(cache_key, illusts)

    return [await il.detail() for il in illusts]


def get_filter_perms(order: str, aspect: str, sanity: str):
    """获取过滤参数"""
    perms = []
    if order in ['n_view', 'n_bookmark']:
        perms.append('fl_order')
    if aspect != 'all':
        perms.append('fl_aspect')
    if sanity != 'all':
        perms.append('fl_sanity')
    return perms


def has_filter_perms(user: User | None, order: str, aspect: str, sanity: str):
    """检查插画过滤权限"""
    perms = get_filter_perms(order, aspect, sanity)

    if not perms:
        return True
    elif user:
        return user.has_perms(*perms)
    else:
        return None


async def tag_rcmd(page: int = 1):
    """主题接口"""
    page = max(page, 1)
    offset = (page - 1) * cfg.PER_PAGE
    query = Tag.cn_tags(visible=True, n_illust__gt=100)
    tags = await query.order_by('-n_illust').limit(cfg.PER_PAGE).offset(offset)
    n_tag = await query.count()  # 获取标签总数
    return tags, n_tag


async def tag_detail(tid: int, order: str = 'id', page: int = 1, aspect: str = 'all', ss: str = 'all', spos=0):
    """查看标签"""
    page = max(page, 1)
    offset = (page - 1) * cfg.PER_PAGE
    if order == 'default':
        order = 'added'

    # 更新浏览次数
    tag = await Tag.get(id=tid)
    tag.n_view += 1
    await tag.save(update_fields=['n_view'])

    illusts = await tag.illusts(order, cfg.PER_PAGE, offset, aspect, get_max_sanity(), ss)
    illusts, spos = IAligner(spos).align(illusts)
    return {'tag': tag, 'illusts': [await il.overview(with_artist=True) for il in illusts]}, spos


async def artist_rcmd(page: int = 1):
    """画师推荐"""
    n_page = cfg.PER_PAGE // 2
    page = max(page, 1)
    offset = (page - 1) * n_page
    # 按最近有作品更新的顺序排序
    artists = await Artist.most_popular(n_page, offset, recent_months=3)
    result = []
    for artist in artists:
        profile = artist.profile
        profile['recent'] = [await i.overview() for i in await artist.recent(9)]
        result.append(profile)
    return result


async def artist_detail(aid: int, page: int = 1, spos=0):
    """画师作品"""
    page = max(page, 1)
    offset = (page - 1) * cfg.PER_PAGE
    artist = await Artist.get(id=aid)
    max_sanity = get_max_sanity()
    illusts = await artist.illusts(max_sanity).order_by('-id').limit(cfg.PER_PAGE).offset(offset)
    illusts, spos = IAligner(spos).align(illusts)
    return {'artist': artist.profile, 'illusts': [await il.overview() for il in illusts], 'sp': spos}


async def search_illust(kw: str, order: str = 'id', page: int = 1, aspect: str = 'all', ss: str = 'all', spos=0):
    """搜索标签"""
    page = max(page, 1)
    offset = (page - 1) * cfg.PER_PAGE
    if order == 'default':
        order = 'added'
    if kw:
        illusts: list[Illust] = await Illust.filter_by_tag(
            kw, order, cfg.PER_PAGE, offset, aspect, get_max_sanity(), ss
        )  # type: ignore
        illusts, spos = IAligner(spos).align(illusts)
        return [await il.overview(with_artist=True) for il in illusts], spos
    else:
        return [], 0


async def search_artist(kw: str, page: int = 1):
    """搜索画师"""
    page = max(page, 1)
    offset = (page - 1) * cfg.PER_PAGE
    result = []
    if kw:
        artists = await Artist.search(kw).limit(cfg.PER_PAGE).offset(offset)

        for artist in artists:
            profile = artist.profile
            profile['recent'] = [await i.overview() for i in await artist.recent(9)]
            result.append(profile)

    return result


async def buy_illust(user: User, iid: int):
    """购买插画"""
    illust = await Illust.get(id=iid)
    if user.has_perms('dl_illust'):
        payment = await user.buy_illust(illust, free=True)
        return {'rc': 0, 'hd_urls': illust.origin_urls}
    else:
        payment = await user.buy_illust(illust)
        return {'rc': 0, 'hd_urls': illust.origin_urls, 'msg': f'次元币 -{payment}'}
