import datetime
import os
import re
from collections import OrderedDict
from enum import IntEnum, StrEnum
from functools import cached_property
from pathlib import Path
from typing import Any, ClassVar, Self

from tortoise import fields
from tortoise.expressions import F, Q
from tortoise.models import Model as BaseModel
from tortoise.queryset import QuerySet, RawSQLQuery
from tortoise.transactions import in_transaction

import common.utils as ut
import config as cfg
from libs.http import State
from libs.orm import PixivModel
from libs.static import get_private_urls, static

__all__ = ['Artist', 'IAligner', 'Illust', 'Ranking', 'Tag', 'TagMap']


class Artist(PixivModel):
    """画师"""

    class Gender(StrEnum):
        male = 'male'
        female = 'female'
        unknown = 'unknown'

    # base info
    name = fields.CharField(max_length=64, db_index=True, description='画师昵称')
    avatar = fields.CharField(max_length=256, default='', description='头像地址')
    account = fields.Char<PERSON>ield(max_length=32, unique=True, description='账户名')
    # more info
    comment = fields.TextField(default='', description='个人简介')
    gender = fields.CharEnumField(Gender, max_length=8, default='unknown', description='性别')
    region = fields.CharField(max_length=64, default='', description='地区')
    # social info
    weibo = fields.CharField(max_length=64, default='', description='Weibo账号')
    twitter = fields.CharField(max_length=64, default='', description='Twitter账号')
    webpage = fields.CharField(max_length=256, default='', description='主页地址')
    # others
    n_illust = fields.IntField(unsigned=True, default=0, db_index=True, description='作品的数量')
    added = fields.DatetimeField(auto_now_add=True, db_index=True, description='添加时间')
    latest_iid = fields.IntField(unsigned=True, db_index=True, null=True, description='画师最新插画作品的ID')

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['id']
        _keymap: ClassVar[dict[str, list[str]]] = {
            'id': ['user', 'id'],
            'name': ['user', 'name'],
            'account': ['user', 'account'],
            'avatar': ['user', 'profile_image_urls', 'medium'],
            'comment': ['user', 'comment'],
            'gender': ['profile', 'gender'],
            'region': ['profile', 'region'],
            'twitter': ['profile', 'twitter_url'],
            'webpage': ['profile', 'webpage'],
            'updated': ['_json_file_time'],
        }

    @classmethod
    async def import_from_dict(cls, fields: dict[str, Any], *keys):
        """从 json 文件导入画师数据"""
        for k, v in fields.items():
            fields[k] = v or cls._meta.fields_map[k].default
        return await super().import_from_dict(fields, *keys)

    async def delete(self):  # type: ignore
        """删除画师"""
        if await self.illusts(7).exists():
            raise ValueError('画师作品不为空，无法删除')
        else:
            return await super().delete()

    @cached_property
    def avturl(self) -> str:
        """头像地址"""
        filename = os.path.basename(self.avatar)
        return static(f'avatar/{filename}')

    @cached_property
    def profile(self):
        """画师信息"""
        if not hasattr(self, '_profile'):
            fields = ['id', 'name', 'avturl', 'comment', 'gender', 'region', 'twitter', 'webpage', 'n_illust']
            self._profile = self.to_dict(only=fields)
        return self._profile

    def illusts(self, max_sanity: int = 2) -> QuerySet['Illust']:
        """画师的作品"""
        return Illust.filter_by_aid(self.id, sanity__lte=max_sanity)

    async def recent(self, num: int):
        """画师近期的作品"""
        max_sanity = State.get('max_sanity', 2)
        return await self.illusts(max_sanity).order_by('-id').limit(num)

    @classmethod
    async def most_popular(cls, limit: int = cfg.PER_PAGE, offset: int = 0, recent_months=6) -> list[Self]:
        """按画师作品的总收藏数排序"""
        if not (isinstance(limit, int) and isinstance(limit, int) and isinstance(recent_months, int)):
            raise TypeError('limit and offset must be int')
        if limit <= 0 or offset < 0 or recent_months <= 0:
            raise ValueError('must satisfy: limit > 0, offset >= 0, recent_months > 0')

        after_date = datetime.date.today() - datetime.timedelta(days=recent_months * 30)

        return await cls.raw(f"""
            SELECT
                artist.*,
                COALESCE(SUM(illust.n_bookmark), 0) AS total_bookmarks
            FROM
                artist
            LEFT JOIN
                illust ON artist.id = illust.aid
            WHERE
                illust.created >= '{after_date:%Y-%m-%d}'
            GROUP BY
                artist.id
            ORDER BY
                total_bookmarks DESC
            LIMIT {limit:d} OFFSET {offset:d};
        """)  # type: ignore # noqa: S608

    async def update_illust_num(self):
        """更新画师的作品数量"""
        result = await self.raw(
            f"""
            UPDATE artist
            SET n_illust = (
                SELECT COUNT(*)
                FROM illust
                WHERE aid = {self.id:d}
            ) WHERE id = {self.id:d};
        """  # noqa: S608
        )
        await self.set_cache()
        return result

    @classmethod
    async def update_all_illust_num(cls):
        """画师的作品数量"""
        result = await cls.raw(
            """
            WITH i_cnt AS (
                SELECT aid, COUNT(*) AS num
                FROM illust
                GROUP BY aid
            )
            UPDATE artist
            SET n_illust = COALESCE(i_cnt.num, 0)
            FROM i_cnt
            WHERE artist.id = i_cnt.aid;
            """
        )
        await cls.rebuild_cache()
        return result

    async def update_latest_iid(self):
        """更新最新作品 ID"""
        result = await self.raw(
            f"""
            UPDATE artist
            SET latest_iid = (
                SELECT MAX(id)
                FROM illust
                WHERE aid = {self.id:d}
            ) WHERE id = {self.id:d};
        """  # noqa: S608
        )
        await self.set_cache()
        return result

    @classmethod
    async def update_all_latest_iid(cls):
        """更新所有画师的最新作品 ID"""
        result = await cls.raw(
            """
            WITH i_latest AS (
                SELECT aid, MAX(id) AS max_id
                FROM illust
                GROUP BY aid
            )
            UPDATE artist
            SET latest_iid = COALESCE(i_latest.max_id, 0)
            FROM i_latest
            WHERE artist.id = i_latest.aid;
            """
        )
        await cls.rebuild_cache()
        return result

    @classmethod
    def search(cls, keyword: str) -> QuerySet[Self]:
        """搜索画师"""
        # 按昵称搜索
        condition = Q(name__icontains=keyword)

        # 搜索 ID
        if keyword.isdecimal():
            condition |= Q(id=int(keyword))

        # 搜索账户名
        if keyword.isascii():
            condition |= Q(account__icontains=keyword)

        return cls.filter(condition)

    @classmethod
    def hot_artists(cls, num: int = cfg.PER_PAGE, days: int = 14, *fields: str):
        """获取热门画师"""
        # 验证字段是否都是模型的合法字段
        safe_fields = [f for f in fields if f in cls._meta.fields]
        select_fields = ','.join(f'"{f}"' for f in safe_fields) if safe_fields else '*'

        since_date = datetime.date.today() - datetime.timedelta(days=days)

        return cls.raw(
            f"""
            SELECT {select_fields} FROM artist WHERE id IN (
                SELECT aid FROM illust WHERE added > '{since_date}' GROUP BY aid ORDER BY count(1) DESC LIMIT {num:d}
            );
        """  # noqa: S608
        )


class Illust(PixivModel):
    """插画"""

    class Level(IntEnum):
        v0 = 0
        v1 = 1
        v2 = 2
        v3 = 3
        v4 = 4
        v5 = 5
        v6 = 6
        v7 = 7

    # Pixiv 基本信息
    aid = fields.IntField(unsigned=True, db_index=True, description='作者ID')
    title = fields.CharField(max_length=128, db_index=True, description='标题')
    caption = fields.TextField(default='', description='介绍')
    width = fields.SmallIntField(unsigned=True, description='插画首图的宽度')
    height = fields.SmallIntField(unsigned=True, description='插画首图的高度')
    page_count = fields.SmallIntField(unsigned=True, default=1, description='插画的图片数量')
    n_bookmark = fields.IntField(unsigned=True, default=0, db_index=True, description='点赞数')
    n_view = fields.IntField(unsigned=True, default=0, db_index=True, description='观看数')
    sanity = fields.IntEnumField(Level, description='作品敏感等级, 取值范围: 0, 2, 4, 6')
    restrict = fields.BooleanField(description='是否是 R18 作品')
    is_ai = fields.BooleanField(default=False, description='是否是AI图')
    visible = fields.BooleanField(default=True, description='是否对用户可见')
    created = fields.DatetimeField(db_index=True, description='创建时间')
    updated = fields.DatetimeField(auto_now_add=True, db_index=True, description='JSON数据的更新时间 (取自文件时间)')
    added = fields.DatetimeField(auto_now_add=True, db_index=True, description='添加时间')
    pixiv_imgs = fields.JSONField(description='Pixiv原图地址, 结构:[{square, medium, large, origin}]')  # type: ignore

    # 补充字段
    w = fields.SmallIntField(unsigned=True, default=0, description='插画首图放大后的宽度')
    h = fields.SmallIntField(unsigned=True, default=0, description='插画首图放大后的高度')
    aspect = fields.FloatField(db_index=True, default=1, description='插画首图的宽高比')
    n_download = fields.IntField(unsigned=True, default=0, db_index=True, description='下载次数')
    main_tag = fields.CharField(max_length=128, default='', description='主标签')
    imgs = fields.JSONField(default={'thumb': '', 'large': [], 'origin': []}, description='图片地址')  # type: ignore

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['-added', '-updated', '-created', 'id']
        _keymap: ClassVar[dict[str, list[str]]] = {
            'id': ['id'],
            'aid': ['user', 'id'],
            'title': ['title'],
            'caption': ['caption'],
            'width': ['width'],
            'height': ['height'],
            'page_count': ['page_count'],
            'n_bookmark': ['total_bookmarks'],
            'n_view': ['total_view'],
            'sanity': ['sanity_level'],  # 取值范围: 0, 2, 4, 6
            'restrict': ['x_restrict'],  # 取值范围: 0, 1, 为 1 说明作品是 R18 级别
            'is_ai': ['illust_ai_type'],
            'visible': ['visible'],
            'created': ['create_date'],
            'image_urls': ['image_urls'],
            'meta_pages': ['meta_pages'],
            'meta_single_page': ['meta_single_page'],
            'updated': ['_json_file_time'],
            # 其他表的字段
            'user': ['user'],
            'tags': ['tags'],
        }

    def __lt__(self, other: 'Illust') -> bool:
        if self.n_bookmark == other.n_bookmark:
            return self.quality < other.quality
        else:
            return self.n_bookmark < other.n_bookmark

    @classmethod
    async def import_from_dict(cls, json_illust):  # type: ignore
        """导入一幅插画作品"""
        async with in_transaction():
            # 提取并导入 User
            user_dict = Artist.extract_fields({'user': json_illust.pop('user')})
            user_dict['updated'] = json_illust['updated']
            await Artist.import_from_dict(user_dict)

            # 提取并导入 Tags
            tags = json_illust.pop('tags')
            if tags:
                for tag_dict in tags:
                    await Tag.import_from_dict(tag_dict, json_illust['id'])
                json_illust['main_tag'] = ut.readable_tag(tags[0]['name'], tags[0]['translated_name'])

            # 导入 Illust
            created = json_illust['created']
            json_illust['created'] = datetime.datetime.fromisoformat(created)
            json_illust['is_ai'] = json_illust.get('is_ai') == 2
            json_illust['visible'] = json_illust['sanity'] < 6  # 敏感等级大于 0 的作品需审核
            json_illust['pixiv_imgs'] = cls.parse_image_urls(json_illust)
            return await super().import_from_dict(json_illust)

    @classmethod
    async def import_extended_data(cls, *ext_data: tuple[int, dict]):
        """导入扩展数据"""
        async with in_transaction():
            for n, (iid, data) in enumerate(ext_data, start=1):
                if illust := await cls.get_or_none(id=iid):
                    print(f'{n:3d}. Update illust ext: {iid}')
                    illust.update_from_dict(data)
                    await illust.save()
                else:
                    print(f'{n:3d}. not exist illust: {iid}')

    @staticmethod
    def id_from_uri(uri: str | Path) -> int:
        """从插画地址中提取插画ID"""
        s_iid = Path(uri).stem.split('_')[0]
        if s_iid.isdecimal():
            return int(s_iid)
        else:
            raise ValueError(f'无效的插画地址: {uri}')

    async def delete(self):  # type: ignore
        """删除一副插画作品"""
        async with in_transaction():
            # 重设画师的作品数量
            await Artist.filter(id=self.aid).update(n_illust=F('n_illust') - 1)
            await Artist.rebuild_cache(self.aid)  # type: ignore

            # 重设标签的作品数量
            t_ids = await TagMap.filter(iid=self.id).values_list('tid', flat=True)
            await Tag.filter(id__in=t_ids).update(n_illust=F('n_illust') - 1)
            await Tag.rebuild_cache(*t_ids)  # type: ignore

            # 删除插画与标签的对应关系
            await TagMap.filter(iid=self.id).delete()

            return await super().delete()

    @cached_property
    def level(self) -> int:
        """数值范围: 0, 2, 4, 6, 7. 该值等于 7 时说明是 R18 作品"""
        return self.restrict + self.sanity

    @cached_property
    def quality(self) -> float:
        """图片质量"""
        if self.n_view < 500:
            return round(self.n_bookmark / 1000 * 100, 2)
        elif self.n_view < 1000:
            return round(self.n_bookmark / 1500 * 100, 2)
        elif self.n_view < 3000:
            return round(self.n_bookmark / 3000 * 100, 2)
        else:
            return round(self.n_bookmark / self.n_view * 100, 2)

    @cached_property
    def resolution(self):
        """分辨率"""
        return f'{self.w} x {self.h}'

    @cached_property
    def price(self) -> int:
        return cfg.PRICE_PER_ILLUST * max(self.level // 2, 1)

    @cached_property
    def thumbnail(self) -> str:
        """缩略图地址"""
        return static(f'small/{self.imgs["thumb"]}')

    @cached_property
    def large_urls(self) -> list[str]:
        """大图地址"""
        urls = []
        for fname in self.imgs['large']:
            path = static('large', fname)
            urls.append(path)
        return urls

    @property
    def origin_urls(self) -> list[str]:
        """原图地址"""
        if cfg.STORAGE in ['b2', 'cloudflare']:
            return get_private_urls('origin', self.imgs['origin'])
        else:
            return [static('origin', fname) for fname in self.imgs['origin']]

    @cached_property
    def n_page(self) -> float:
        """实际收录的图片数量"""
        return len(self.imgs['origin'])

    async def artist(self) -> Artist:
        """插画作者"""
        if not hasattr(self, '_artist'):
            self._artist = await Artist.get(id=self.aid)
        return self._artist

    async def tags(self) -> list['Tag']:
        """插图的标签 (按 Tag.n_illust 降序排列)"""
        if not hasattr(self, '_tags'):
            self._tags: list = await Tag.raw(
                f"""
                SELECT * FROM tag WHERE id IN (
                    SELECT tid FROM tagmap WHERE iid = {self.id:d}
                ) AND visible = true order by n_illust desc;
            """  # noqa: S608
            )
        return self._tags  # type: ignore

    async def overview(self, with_artist: bool = False) -> dict[str, Any]:
        """插画概览"""
        result = self.to_dict(
            only=['id', 'title', 'n_page', 'n_bookmark', 'is_ai', 'level', 'thumbnail', 'aspect', 'main_tag']
        )
        if with_artist:
            result['artist'] = (await self.artist()).profile
        return result

    async def detail(self) -> dict[str, Any]:
        """插画详情"""
        result = self.to_dict(
            only=('id', 'title', 'caption', 'n_bookmark', 'n_view', 'is_ai', 'created', 'imgs', 'main_tag', 'w', 'h'),
            extra=('level', 'resolution', 'price', 'large_urls', 'n_page'),
        )
        artist = await self.artist()
        result['artist'] = artist.profile
        result['artist']['recent'] = [await i.overview() for i in await artist.recent(6)]

        # 封装 Tag 列表
        tags_dict = OrderedDict()
        for t in await self.tags():
            if t.cname not in tags_dict:
                # tags 按 n_illust 降序排列, cname 相同时, 忽略后面 n_illust 小的 tag
                tags_dict[t.cname] = t
        result['tags'] = [(t.id, t.cname) for t in tags_dict.values()]
        result['stags'] = ', '.join(f'{t[1]}' for t in result['tags'])
        return result

    async def set_visible(self, visible: bool):
        """修改可见性"""
        self.visible = visible
        return await self.save(update_fields=['visible'])

    @classmethod
    def filter_by_aid(cls, aid: int, **kwargs) -> QuerySet[Self]:
        """根据画师ID搜索插画"""
        return cls.filter(aid=aid, **kwargs)

    @classmethod
    def gen_aspect_filter_expr(cls, aspect: str | None) -> str:
        """生成宽高比表达式"""
        match aspect:
            case 'ver':
                return 'AND il.aspect < 0.9'
            case 'hor':
                return 'AND il.aspect > 1.1'
            case 'sqr':
                return 'AND 0.9 <= il.aspect and il.aspect <= 1.1'
            case _:
                return ''

    @staticmethod
    def gen_sanity_filter_expr(max_sanity: int, sanity: str) -> str:
        """检查涩图等级"""
        default = f'AND il.sanity <= {max_sanity:d}'
        match sanity:
            case '2':
                return 'AND il.sanity <= 2'
            case '4':
                return 'AND il.sanity = 4' if max_sanity >= 4 else default
            case '6':
                return 'AND il.sanity >= 6' if max_sanity >= 6 else default
            case _:
                return default

    @classmethod
    def filter_by_tid(
        cls,
        tid: int,
        order: str = 'id',
        num: int = cfg.PER_PAGE,
        offset: int = 0,
        aspect=None,
        max_sanity: int = 2,
        sanity: str = 'all',
    ) -> RawSQLQuery:
        """根据标签ID搜索插画"""
        expr_aspect_filter = cls.gen_aspect_filter_expr(aspect)
        expr_sanity_filter = cls.gen_sanity_filter_expr(max_sanity, sanity)

        if order not in cls.fields():
            order = 'id'

        return cls.raw(
            f"""
            SELECT il.* FROM illust il JOIN tagmap tm ON il.id = tm.iid
            WHERE tm.tid = {tid:d}
            {expr_aspect_filter}
            {expr_sanity_filter}
            ORDER BY il.{order} DESC
            LIMIT {num:d} OFFSET {offset:d};
        """  # noqa: S608
        )

    @classmethod
    def filter_by_tag(
        cls,
        name: str,
        order: str = 'id',
        num: int = cfg.PER_PAGE,
        offset: int = 0,
        aspect=None,
        max_sanity: int = 2,
        sanity: str = 'all',
    ) -> RawSQLQuery:
        """根据标签名搜索插画"""
        expr_aspect_filter = cls.gen_aspect_filter_expr(aspect)
        expr_sanity_filter = cls.gen_sanity_filter_expr(max_sanity, sanity)
        name = name.replace("'", "''")

        return cls.raw(
            f"""
            SELECT il.* FROM illust il JOIN tagmap tm ON il.id = tm.iid
            WHERE tm.tid = (
                SELECT id FROM tag
                WHERE (
                    name like '%{name}%'
                    OR trans like '%{name}%'
                    OR cname like '%{name}%'
                )
                ORDER BY n_illust DESC LIMIT 1
            )
            {expr_aspect_filter}
            {expr_sanity_filter}
            ORDER BY {order} DESC LIMIT {num:d} OFFSET {offset:d};
        """  # noqa: S608
        )

    async def related(self, max_sanity: int = 2, num: int = cfg.PER_PAGE, offset: int = 0):
        """获取与当前插画相关的插画"""
        return await self.raw(
            f"""
            WITH
            i_tags AS ( SELECT tid FROM tagmap WHERE iid = {self.id:d} ),
            t_illusts AS (
                SELECT
                    tm.tid, tm.iid,
                    ROW_NUMBER() OVER (PARTITION BY tm.tid ORDER BY tm.iid DESC) AS rn
                FROM tagmap tm
                JOIN i_tags i_tg
                ON tm.tid = i_tg.tid
                WHERE tm.iid != {self.id:d}
            ),
            related_iids AS ( SELECT DISTINCT iid FROM t_illusts WHERE rn <= {num // 2:d} )
            SELECT i.*
            FROM related_iids r JOIN illust i
            ON r.iid = i.id WHERE i.sanity <= {max_sanity:d}
            ORDER BY i.id DESC
            LIMIT {num:d} OFFSET {offset:d};
        """  # noqa: S608
        )

    @staticmethod
    def parse_image_urls(illust_dict) -> list[dict[str, Any]]:
        """解析 pixiv 原图不同分辨率的地址"""
        if illust_dict['page_count'] == 1:
            img_urls = [
                {
                    'square': illust_dict['image_urls']['square_medium'],
                    'medium': illust_dict['image_urls']['medium'],
                    'large': illust_dict['image_urls']['large'],
                    'origin': illust_dict['meta_single_page']['original_image_url'],
                }
            ]
        else:
            img_urls = []
            for item in illust_dict['meta_pages']:
                img_urls.append({
                    'square': item['image_urls']['square_medium'],
                    'medium': item['image_urls']['medium'],
                    'large': item['image_urls']['large'],
                    'origin': item['image_urls']['original'],
                })
        return img_urls


class Tag(PixivModel):
    """标签"""

    name = fields.CharField(max_length=128, unique=True, description='标签原文')
    trans = fields.CharField(max_length=128, default='', db_index=True, description='标签译文')
    cover = fields.CharField(max_length=256, default='', description='封面')
    n_illust = fields.IntField(unsigned=True, default=0, db_index=True, description='使用该标签作品的数量')
    n_view = fields.IntField(unsigned=True, default=0, db_index=True, description='浏览次数')
    visible = fields.BooleanField(default=False, description='是否对用户可见')
    cname = fields.CharField(max_length=128, default='', db_index=True, description='筛选的可读性更好的标签')

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['-n_illust', '-n_view']
        _keymap: ClassVar[dict[str, list[str]]] = {'name': ['name'], 'trans': ['translated_name']}
        _bookmark_tag = re.compile(r'\d0+收藏|\d0+users|\d0+.+bookmarks', re.IGNORECASE)

    def __str__(self) -> str:
        return f'<Tag: {self.cname}>'

    def __lt__(self, other_tag: 'Tag') -> bool:
        return (self.n_view, self.n_illust) < (other_tag.n_view, other_tag.n_illust)

    @classmethod
    async def import_from_dict(cls, tag_dict: dict[str, str], iid: int):  # type: ignore
        """从作品数据更新 Tag 信息"""
        name = ut.replace_cn_punctuation(tag_dict['name'].strip())
        trans = ut.replace_cn_punctuation((tag_dict['translated_name'] or '').strip())

        attrs: dict[str, Any] = {'trans': trans} if trans else {}
        attrs['visible'] = not (ut.is_sensitive(name) or ut.is_sensitive(trans) or cls.is_bookmark_tag(name, trans))
        attrs['cname'] = ut.readable_tag(name, trans)

        tag, _ = await cls.update_or_create(name=name, defaults=attrs)
        return await TagMap.update_or_create(tid=tag.id, iid=iid)

    @classmethod
    def is_bookmark_tag(cls, *names: str) -> bool:
        """是否是收藏标签"""
        for name in names:
            if cls.Meta._bookmark_tag.search(name):
                return True
        return False

    async def delete(self):  # type: ignore
        """删除一个标签"""
        async with in_transaction():
            await TagMap.filter(tid=self.id).delete()
            return await super().delete()

    @cached_property
    def fullname(self) -> str:
        """完整的标签名"""
        if not self.trans:
            return self.name
        elif self.cname == self.trans:
            return f'{self.trans}（{self.name}）'
        else:
            return f'{self.name}（{self.trans}）'

    def to_dict(self):  # type: ignore
        fields = ['id', 'cname', 'cover']
        return super().to_dict(only=fields)

    async def illusts(
        self,
        order: str = 'id',
        num: int = cfg.PER_PAGE,
        offset: int = 0,
        aspect=None,
        max_sanity: int = 2,
        sanity=None,
    ) -> list[Illust]:
        """Tag 对应的插图"""
        return await Illust.filter_by_tid(self.id, order, num, offset, aspect, max_sanity, sanity)  # type: ignore

    async def update_illust_num(self):
        """更新标签作品数量"""
        result = await self.raw(
            f"""
            UPDATE tag SET n_illust = (
                SELECT COUNT(*)
                FROM tagmap
                WHERE tid = {self.id:d}
            ) WHERE id = {self.id:d};
        """  # noqa: S608
        )
        await self.set_cache()
        return result

    @classmethod
    async def update_all_illust_num(cls):
        """更新标签作品数量"""
        result = await cls.raw(
            """
            WITH tag_cnt AS (
                SELECT tagmap.tid, count(*) AS num
                FROM tagmap
                GROUP BY tagmap.tid
            )
            UPDATE tag
            SET n_illust = tag_cnt.num
            FROM tag_cnt
            WHERE tag.id = tag_cnt.tid;
        """
        )
        await cls.rebuild_cache()
        return result

    @classmethod
    async def set_cover(cls, tid, iid=None):
        """设置 Tag 的封面 URL"""
        if iid is None:
            date = str(datetime.date.today() - datetime.timedelta(days=180))
            result = cls.raw(
                f"""
                UPDATE tag SET cover = iu.url
                FROM (
                    SELECT
                        imgs ->> 'thumb' AS url
                    FROM illust
                    WHERE
                        id IN (SELECT iid FROM tagmap WHERE tid = {tid:d})
                        AND visible = true
                        AND created >= '{date}'
                        AND aspect > 1
                    ORDER BY n_bookmark DESC
                    LIMIT 1
                ) AS iu WHERE tag.id={tid:d};
            """  # noqa: S608
            )
        else:
            result = cls.raw(
                f"""
                UPDATE tag SET cover = iu.url
                FROM (
                    SELECT
                        imgs ->> 'thumb' AS url
                    FROM illust
                    WHERE id = {iid:d}
                ) AS iu WHERE tag.id={tid:d};
            """  # noqa: S608
            )
        await cls.rebuild_cache(tid)
        return result

    @classmethod
    async def update_all_cover(cls, min_illust_contains=100, within_days=365 * 3, rank=2, only_empty=True):
        """更新全部 Tag 的封面"""
        date = str(datetime.date.today() - datetime.timedelta(days=within_days))
        selected = "AND tag.cover = '' " if only_empty else ''
        result = await cls.raw(
            f"""
            WITH tu AS (
                SELECT tid, url FROM (
                    SELECT
                        tm.tid,
                        il.imgs ->> 'thumb' AS url,
                        rank() OVER (
                            PARTITION BY tm.tid ORDER BY il.n_bookmark DESC
                        ) AS rk
                    FROM illust AS il JOIN (
                        SELECT tid, iid FROM tagmap WHERE tid IN (
                            SELECT tid FROM tagmap GROUP BY tid
                            HAVING count(1) > {min_illust_contains:d}
                        )
                    ) AS tm ON il.id = tm.iid
                    WHERE
                        il.sanity <= 4
                        AND il.visible = true
                        AND il.created >= '{date}'
                        AND il.aspect > 1.1
                ) AS imgs WHERE imgs.rk = {rank:d}
            )
            UPDATE tag
            SET cover = tu.url
            FROM tu
            WHERE
                tag.id = tu.tid
                {selected};
        """  # noqa: S608
        )
        await cls.rebuild_cache()
        return result

    async def set_cname(self):
        """设置 Tag 的 cname"""
        self.cname = ut.readable_tag(self.name, self.trans)
        await self.save(update_fields=['cname'])

    @classmethod
    async def update_all_cname(cls):
        """更新所有 Tag 的 cname"""
        async with in_transaction():
            for tag in await cls.all():
                tag.cname = ut.readable_tag(tag.name, tag.trans)
                await tag.save(update_fields=['cname'])

    @classmethod
    def search(cls, kw: str) -> QuerySet[Self]:
        """搜索标签"""
        condition = Q(name=kw) | Q(trans=kw)
        return cls.filter(condition)

    @classmethod
    def cn_tags(cls, **kwargs):
        """中文标签"""
        condition = Q(name__posix_regex=r'[\u4e00-\u9fff]') | Q(trans__posix_regex=r'[\u4e00-\u9fff]')
        fields = cls.fields()
        for key, value in kwargs.items():
            if key in fields:
                condition &= Q(**{key: value})
        return cls.filter(condition)


class TagMap(BaseModel):
    """Tag 与 Illust 关系表"""

    tid = fields.IntField(unsigned=True, db_index=True)
    iid = fields.IntField(unsigned=True, db_index=True)

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['tid', 'iid']
        unique_together = ('tid', 'iid')

    def __str__(self) -> str:
        return f'<TagMap: {self.tid}-{self.iid}>'


class Ranking(PixivModel):
    """一张插画的排名情况"""

    date = fields.DateField(db_index=True, description='榜单日期')
    rank = fields.SmallIntField(unsigned=True, db_index=True, description='当日排行')
    y_rank = fields.SmallIntField(unsigned=True, default=0, db_index=True, description='前一天的排行 (0: 当天未上榜)')
    aid = fields.IntField(unsigned=True, db_index=True, description='Artist ID')
    name = fields.CharField(db_index=True, max_length=64, description='画师昵称')
    iid = fields.IntField(unsigned=True, db_index=True, description='Illust ID')
    width = fields.SmallIntField(unsigned=True, description='插画首图的宽度')
    height = fields.SmallIntField(unsigned=True, description='插画首图的高度')
    page_count = fields.SmallIntField(unsigned=True, description='插图数量')
    thumbnail = fields.CharField(max_length=256, default='', description='缩略图网址')
    hidden = fields.BooleanField(default=True, description='是否已隐藏')

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['date', 'rank']
        unique_together = ('date', 'rank')
        _keymap: ClassVar[dict[str, list[str]]] = {
            'date': ['date'],
            'rank': ['rank'],
            'y_rank': ['yes_rank'],
            'aid': ['user_id'],
            'name': ['user_name'],
            'iid': ['illust_id'],
            'width': ['width'],
            'height': ['height'],
            'page_count': ['illust_page_count'],
            'thumbnail': ['url'],
        }

    def __str__(self) -> str:
        return f'<Ranking: {self.date}-{self.rank}>'

    @classmethod
    async def import_from_json(cls, jsonpath: Path, *args, **kwargs):  # type: ignore
        """从 json 文件导入榜单数据"""
        jsondata = cls.load_json(jsonpath)

        # NOTE
        # ranking 数据中每一项的 date 为图片的发布日期。
        # 这里将其替换为 “榜单日期”。
        date = datetime.datetime.strptime(jsonpath.stem, '%Y%m%d').date()

        for rk_item in jsondata:
            print(f'       adding rank {rk_item["rank"]}', end='\r')
            rk_item['date'] = date
            fields = cls.extract_fields(rk_item)
            await cls.import_from_dict(fields, 'date', 'rank')
        print()

    @classmethod
    def get_ranking(cls, date: datetime.date, num: int, offset: int, only_new=False):
        """获取指定日期的排行榜"""
        only_new_expr = 'AND y_rank = 0' if only_new else ''
        return Illust.raw(
            f"""
            SELECT * FROM illust WHERE id IN (
                SELECT iid FROM ranking
                WHERE date = '{date}' {only_new_expr}
                LIMIT {num:d} OFFSET {offset:d}
            );"""  # noqa: S608
        )


class IAligner:
    def __init__(self, pos: int = 0):
        self.slot_v: Illust | None = None  # type: ignore
        self.slot_h: Illust | None = None  # type: ignore
        self.pos = 1 if pos == 1 else 0
        self._temp: list[Illust] = []

    @staticmethod
    def calc(il: Illust | None) -> int:
        if il is None:
            return 0
        elif il.aspect <= 1.1:
            return 1
        else:
            return 2

    def add(self, il: Illust):
        if self.pos == 0:
            self.slot_v = il
            self.pos = 1
        else:
            self.slot_h = il
            self.pos = 0

    def pop(self):
        s = self.calc(self.slot_v), self.calc(self.slot_h)

        match s:
            case (0, 0) | (1, 0):
                r = []
            case (1, 1) | (2, 0) | (0, 1):
                r = self.reset()
            case (1, 2):
                r, self.slot_h = [self.slot_h, *self._temp], None
                self.pos, self._temp = 1, []
            case (0, 2):
                self._temp.append(self.slot_h)  # type: ignore
                r, self.slot_h, self.pos = [], None, 1
            case _:
                raise Exception(f'unexpected status: {s=} [{self.slot_v}, {self.slot_h}]')
        return r

    def reset(self):
        r = [self.slot_v, self.slot_h, *self._temp]
        self.slot_v, self.slot_h, self._temp, self.pos = None, None, [], 0
        return [i for i in r if i is not None]

    def align(self, illusts: list[Illust]) -> tuple[list[Illust], int]:
        new = []
        for il in illusts:
            self.add(il)
            if r := self.pop():
                new += r
        new += self._temp
        r = [self.slot_v, self.slot_h]
        new += [i for i in r if i is not None]
        return new, self.pos
