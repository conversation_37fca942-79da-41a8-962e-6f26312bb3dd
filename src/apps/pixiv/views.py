from urllib.parse import urlencode

from fastapi import Request
from fastapi.responses import HTMLResponse

from apps.album import apis as a_api
from apps.pixiv import apis as p_api
from common.decorator import page_hits
from common.notice import get_notice
from libs.http import redirect, render


async def home():
    """首页"""
    num = 12
    tags, _ = await p_api.tag_rcmd()  # 获取热门标签
    result = {
        'albums': (await a_api.album_rcmd())[:num],  # 获取专辑推荐
        'tags': tags[: num + 9],  # 获取热门标签
        'artists': (await p_api.artist_rcmd())[:num],  # 获取热门画师
        'illusts': (await p_api.illust_rcmd())[0],  # 获取推荐插画
        'notices': await get_notice(),  # 获取公告
    }
    return render('apps/home.html', **result)


@page_hits('search', 'kw', 'stype')
async def search(
    request: Request,
    kw: str,
    stype: str = 'illust',
    order: str = 'added',
    page: int = 1,
    aspect: str = 'all',
    ss: str = 'all',
    sp: int = 0,
):
    """
    搜索

    order: added / created / n_view / n_bookmark
    aspect: all / ver / hor / sqr
    ss: all / 2 / 4 / 6
    """
    err = ''
    items = []
    if stype == 'illust':
        match p_api.has_filter_perms(request.user, order, aspect, ss):
            case None:
                backto = urlencode({'backto': request.url})
                err = f'登录后才能使用此功能哦！<a href="/w/user/login/?{backto}">请点击这里</a>'
            case False:
                err = '这需要 “会员” 权限哦！<a href="/w/purchase/">查看详情</a>'
            case True:
                items, sp = await p_api.search_illust(kw, order, page, aspect, ss=ss, spos=sp)
    elif stype == 'artist':
        items = await p_api.search_artist(kw, page)
    elif stype == 'album':
        items = await a_api.search_album(kw, page)

    if page > 1:
        return render(f'apps/i_{stype}s.html', illusts=items, artists=items, albums=items, sp=sp)
    else:
        return render('apps/search.html', items=items, kw=kw, stype=stype, order=order, err=err, sp=sp)


async def illust_rcmd(
    request: Request,
    page: int = 1,
    order: str = 'added',
    aspect: str = 'all',
    ss: str = 'all',
    sp: int = 0,
):
    """推荐插画"""
    illusts, err = [], ''
    match p_api.has_filter_perms(request.user, order, aspect, ss):  # 检查用户权限
        case None:
            backto = urlencode({'backto': request.url})
            err = f'登录后才能使用此功能哦！<a href="/w/user/login/?{backto}">请点击这里</a>'
        case False:
            err = '这需要 “会员” 权限哦！<a href="/w/purchase/">查看详情</a>'
        case True:
            illusts, sp = await p_api.illust_rcmd(order=order, page=page, aspect=aspect, ss=ss, spos=sp)  # 获取推荐插画

    template = 'apps/i_illusts.html' if page > 1 else 'apps/illusts.html'
    ad = False if page > 1 else True
    return render(template, illusts=illusts, ad=ad, sp=sp, err=err)


@page_hits('illust', 'iid')
async def illust(request: Request, iid: int):
    """作品详情"""
    illust = await p_api.idetail(iid)
    if p_api.get_max_sanity() >= illust['level']:
        return render('apps/illust.html', illust=illust)
    elif request.user:
        return redirect('/w/purchase/?err=您需要升级 “会员”<br>才能查看哦！')
    else:
        return redirect(f'/w/user/login/?backto={request.url.path}&notice=true')


async def irelated(iid: int):
    """相关插画"""
    illusts, _ = await p_api.irelated(iid)
    return render('apps/i_illusts.html', illusts=illusts, disable_dynamic=True)


async def artist_rcmd(page: int = 1):
    """画师推荐"""
    artists = await p_api.artist_rcmd(page)
    template = 'apps/i_artists.html' if page > 1 else 'apps/artists.html'
    return render(template, artists=artists)


@page_hits('artist', 'aid')
async def artist(request: Request, aid: int, page: int = 1, sp: int = 0) -> HTMLResponse:
    """画师详情"""
    detail = await p_api.artist_detail(aid, page=page, spos=sp)
    detail['show_intro'] = True
    template = 'apps/i_illusts.html' if page > 1 else 'apps/artist.html'
    detail['ad'] = False if page > 1 else True
    return render(template, **detail)


async def tag_rcmd(page: int = 1):
    """标签推荐"""
    tags, n_tag = await p_api.tag_rcmd(page)
    return render('apps/tags.html', tags=tags, n_tag=n_tag)


@page_hits('tag', 'tid')
async def tag(
    request: Request,
    tid: int,
    page: int = 1,
    order: str = 'added',
    aspect: str = 'all',
    ss: str = 'all',
    sp: int = 0,
):
    """
    标签详情

    order: added / created / n_view / n_bookmark
    aspect: all / ver / hor / sqr
    ss: all / 2 / 4 / 6
    """
    detail, err = {'tag': '*', 'illusts': []}, ''
    match p_api.has_filter_perms(request.user, order, aspect, ss):
        case None:
            backto = urlencode({'backto': request.url})
            err = f'登录后才能使用此功能哦！<a href="/w/user/login/?{backto}">请点击这里</a>'
        case False:
            err = '这需要 “会员” 权限哦！<a href="/w/purchase/">查看详情</a>'
        case True:
            detail, sp = await p_api.tag_detail(tid, order, page, aspect, ss=ss, spos=sp)

    template = 'apps/i_illusts.html' if page > 1 else 'apps/tag.html'
    detail['ad'] = False if page > 1 else True  # type: ignore
    return render(template, err=err, sp=sp, **detail)  # type: ignore


async def download_illust(request: Request, iid: int):
    """下载作品"""
    return await p_api.buy_illust(request.user, iid)
