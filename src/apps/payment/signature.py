from base64 import b64decode, b64encode
from hashlib import md5
from typing import Any

from cryptography.exceptions import InvalidSignature
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.asymmetric.rsa import RSAPrivateKey, RSAPublicKey

from config import MD5_KEY, PRIVATE_KEY, PUBLIC_KEY

__all__ = ['gen_sign_bytes', 'md5_sign', 'md5_verify', 'rsa_sign', 'rsa_verify', 'verify_signature']


def gen_sign_bytes(params: dict[str, Any]) -> bytes:
    """
    生成待签名字符串

    1. params 为请求报文所有非空请求参数, 不包括数组、字节类型参数, 如文件、字节流, 剔除 sign、sign_type 字段
    2. 按照第一个字符的键值 ASCII 码递增排序（字母升序排序）, 如果遇到相同字符则按照第二个字符的键值 ASCII 码递增排序, 以此类推
    3. 将排序后的参数和对应值, 组合成“参数=参数值”的格式, 并且把这些参数用 & 字符连接起来
    4. 将待签名字符串编码为 bytes 类型

    :param params: 请求参数字典
    :return: 待签名字符串
    """
    filtered_params = {
        k: v
        for k, v in params.items()
        if isinstance(v, (str, int, float))  # 只允许有字符串、整数和浮点数类型
        and k not in ('sign', 'sign_type')  # 排除sign和sign_type字段
    }

    str_params = '&'.join(f"{k}={v}" for k, v in sorted(filtered_params.items()))
    return str_params.encode('utf-8')


def rsa_sign(params: dict[str, Any], private_key: RSAPrivateKey = PRIVATE_KEY) -> str:
    """
    对参数进行签名

    1. 将参数拼接成待签名字符串
    2. 使用商户私钥, 对待签名字符串计算RSA签名（SHA256WithRSA）, 得到签名 sign

    :param params: 请求参数字典
    :return: 签名结果(十六进制字符串)
    """
    # 生成待签名字符串
    sign_string = gen_sign_bytes(params)

    # RSA签名（SHA256WithRSA）
    signature = private_key.sign(sign_string, padding.PKCS1v15(), hashes.SHA256())

    # 将签名结果转换为 Base64 编码的字符串
    return b64encode(signature).decode('utf-8')


def rsa_verify(params: dict[str, Any], signature: str, public_key: RSAPublicKey = PUBLIC_KEY) -> bool:
    """
    验证签名

    1. 将参数拼接成待签名字符串
    2. 使用平台公钥, 根据签名字符串sign, 对待签名字符串与进行RSA验签（SHA256WithRSA）

    :param params: 请求参数字典
    :param signature: 签名值(十六进制字符串)
    :return: 验证结果
    """
    # 生成待签名字符串
    sign_string = gen_sign_bytes(params)

    try:
        # RSA验签
        public_key.verify(b64decode(signature.encode('utf-8')), sign_string, padding.PKCS1v15(), hashes.SHA256())
        return True
    except (InvalidSignature, ValueError):
        return False


def md5_sign(params: dict[str, Any], secret_key: bytes = MD5_KEY):
    """
    1. 将参数拼接成待签名字符串，参数值不要进行url编码。
    2. 再将拼接好的字符串与商户密钥KEY进行MD5加密得出sign签名参数，sign = md5 ( a=b&c=d&e=f + KEY ) ，md5结果为小写。
    """
    # 生成待签名字符串
    sign_string = gen_sign_bytes(params) + secret_key
    # MD5加密
    return md5(sign_string).hexdigest()  # noqa: S324


def md5_verify(params: dict[str, Any], signature: str, secret_key: bytes = MD5_KEY):
    """
    验证 MD5 签名

    1. 先对参数进行签名
    2. 将签名结果与传入的签名进行比较
    """
    return md5_sign(params, secret_key) == signature


def verify_signature(params: dict[str, Any], sign_type: str, signature: str):
    """验证签名"""
    match sign_type.upper():
        case 'RSA':
            return rsa_verify(params, signature)
        case 'MD5':
            return md5_verify(params, signature)
        case _:
            raise ValueError(f'Unknown sign type: {sign_type}')
