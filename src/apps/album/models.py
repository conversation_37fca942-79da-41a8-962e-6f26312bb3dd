from enum import StrEnum
from functools import cached_property
from typing import ClassVar, Self

from tortoise import fields
from tortoise.expressions import Q
from tortoise.queryset import QuerySet

import config as cfg
from common import utils as ut
from libs.orm import Model
from libs.static import static

__all__ = ['Album', 'Category']


class Category(StrEnum):
    """专辑分类"""

    acg_album = '插画精选'
    cos_album = 'Cosplay'
    acg_gallery = '插画合辑'
    cos_gallery = 'Coser全辑'
    acg_base = '二次元图库'
    cos_base = 'Cosplay图库'

    @classmethod
    def get_catg(cls, flag: str, *, gallery: bool = False):
        """根据flag和是否为画集返回分类"""
        if flag.lower() == 'acg':
            return cls.acg_gallery if gallery else cls.acg_album
        elif flag.lower() == 'cos':
            return cls.cos_gallery if gallery else cls.cos_album
        else:
            raise ValueError(f'Invalid flag: {flag}')


class Album(Model):
    """插画专辑"""

    # 基本信息
    title = fields.CharField(max_length=150, unique=True, db_index=True, description='专辑名称')
    catg = fields.CharEnumField(Category, description='专辑分类')
    level = fields.SmallIntField(unsigned=True, description='专辑等级')
    description = fields.TextField(default='', description='专辑概述')
    samples: list[str] = fields.JSONField(default=[], description='插画样张的 URL 列表')  # type: ignore
    size = fields.IntField(unsigned=True, default=0, description='压缩包大小, 单位: 字节')
    n_img = fields.IntField(unsigned=True, db_index=True, description='图片数量')
    n_view = fields.IntField(unsigned=True, default=0, db_index=True, description='浏览次数')
    n_download = fields.IntField(unsigned=True, default=0, db_index=True, description='下载次数')
    s_tags = fields.CharField(max_length=256, default='', description='标签字符串，以逗号分隔')
    # 隐藏内容
    url = fields.CharField(max_length=256, description='压缩包 URL')
    password = fields.CharField(max_length=32, default=cfg.FAV_DOMAIN, description='解压码')
    extrcode = fields.CharField(max_length=32, description='提取码')
    # 时间
    created = fields.DatetimeField(auto_now_add=True, description='发布时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['id']

    @cached_property
    def pretty_size(self) -> str:
        """用来展示的专辑大小"""
        return ut.readable_size(self.size)

    @cached_property
    def price(self) -> int:
        """专辑价格"""
        if self.catg in [Category.acg_album, Category.acg_gallery]:
            return round(self.n_img * cfg.PRICE_PER_ILLUST * self.level * 0.65)
        else:
            unit = max(1, round(self.n_img / 50))
            mul = 1  # max(1, self.level // 2)
            return round(cfg.PRICE_COSPLAY_ALBUM * unit * mul)

    @cached_property
    def tags(self) -> list[str]:
        """标签列表"""
        return [tag.strip().title() for tag in self.s_tags.split(',') if tag.strip()]

    @cached_property
    def thumbnail(self) -> str:
        """缩略图地址"""
        return static(f'album/{self.samples[0]}')

    @cached_property
    def large_urls(self) -> list[str]:
        """大图地址"""
        urls = []
        for fname in self.samples:
            path = static('album', fname)
            urls.append(path)
        return urls

    @classmethod
    def search(cls, keyword: str) -> QuerySet[Self]:
        """搜索专辑"""
        keyword = keyword.strip().lower()
        condition = Q(title__icontains=keyword) | Q(description__icontains=keyword) | Q(s_tags__icontains=keyword)
        return cls.filter(condition)

    @cached_property
    def free_vip(self):
        """免费下载专辑的条件"""
        match self.catg:
            case Category.acg_album:
                return cfg.Vip.get(cfg.PERMISSIONS['dl_acg_album'][0])
            case Category.cos_album:
                return cfg.Vip.get(cfg.PERMISSIONS['dl_cos_album'][0])
            case Category.acg_gallery:
                return cfg.Vip.get(cfg.PERMISSIONS['dl_acg_gallery'][0])
            case Category.cos_gallery:
                return cfg.Vip.get(cfg.PERMISSIONS['dl_cos_gallery'][0])
            case Category.acg_base:
                return cfg.Vip.get(cfg.PERMISSIONS['dl_acg_base'][0])
            case Category.cos_base:
                return cfg.Vip.get(cfg.PERMISSIONS['dl_cos_base'][0])
            case _:
                raise ValueError
