#!/usr/bin/env python
"""专辑/画集 Meta 信息及相关处理

专辑/画集数据目录结构
AlbumMeta
├── acg_album
│    ├── 原神-甘雨-001/
│    │    ├── meta.json
│    │    ├── Smp_asdf.jpg
│    │    └── Smp_fdas.jpg
│    ├── 约战-时崎狂三-002/
│    │    ├── meta.json
│    │    ├── Smp_oihsd.jpg
│    │    └── Smp_jhsdu.jpg
│    └── ...
├── cos_album
│    ├── 桜桃喵-0001-轻纱/
│    │    ├── meta.json
│    │    ├── Smp_asdf.jpg
│    │    └── Smp_fdas.jpg
│    ├── Shika小鹿鹿-0001-蒂法
│    │    ├── meta.json
│    │    ├── Smp_oihsd.jpg
│    │    └── Smp_jhsdu.jpg
│    └── ...
├── albums.json
└── images
"""

import json
import operator
import os
from functools import cached_property
from pathlib import Path

from PIL import Image

from apps.album.models import Category
from common import utils as ut
from common.categories import ALBUM_TAGS
from config import BASE_DIR, FAV_DOMAIN
from libs.terminal import fill_str, print_dbg, print_inf, read_lines

BASIC_DIR = Path(os.environ['G'])
META_DIR = BASIC_DIR / 'AlbumMeta'  # 所有专辑的元数据目录
META_IMAGES = META_DIR / 'images'  # 所有样图的汇总目录
JSON_COMP = META_DIR / 'albums.json'  # 所有 json 的汇总文件
ACG_LARGE = Path(os.environ['I']) / 'IBase' / 'large'
ZIP_DIR = Path(os.environ['D'])


ADDITION = list(Path(BASE_DIR).glob('tools/addition/*'))

SAMPLE_PREFIX = 'Smp_'


__all__ = ['AlbumMeta', 'GalleryMeta', 'Meta']

META_IMAGES.mkdir(parents=True, exist_ok=True)
JSON_COMP.touch()


class Meta:
    """元数据基类"""

    def __init__(self, asset_path: Path | str, catg: Category, password: str = FAV_DOMAIN):
        self.asset_path = (Path(asset_path) if isinstance(asset_path, str) else asset_path).absolute()
        self.catg = catg
        self.level = 9
        self.description = ''
        self.s_tags = ''
        self.password = password
        self.url = ''
        self.extrcode = ''

        self.meta_path = META_DIR / catg.name / self.title
        self.meta_path.mkdir(parents=True, exist_ok=True)
        self.load()

    def __str__(self):
        return f'「{self.catg}：{self.title}」'

    def load(self):
        """加载元数据"""
        with self.json_path.open() as fp:
            try:
                meta_data = json.load(fp)
            except json.JSONDecodeError:
                meta_data = {}
        if meta_data:
            self.update(**meta_data)

    @cached_property
    def title(self):
        """专辑标题"""
        base_name = self.asset_path.stem.replace(' ', '_')

        if self.catg == Category.acg_album:
            return f'{base_name}精选集_[{self.n_img}P_{self.pretty_size}]'
        elif self.catg == Category.acg_gallery:
            return f'{base_name}合辑_[{self.n_img}P_{self.pretty_size}]'

        return base_name

    @cached_property
    def images(self):
        """获取所有图片路径"""
        return list(ut.find_all_images(self.asset_path))

    @cached_property
    def n_img(self):
        """图片数量"""
        return len(self.images)

    @property
    def samples(self):
        """样本图片路径"""
        return [path for path in ut.find_all_images(self.meta_path) if path.name.startswith(SAMPLE_PREFIX)]

    @cached_property
    def size(self):
        """所有图片大小"""
        return sum(ipath.stat().st_size for ipath in self.images)

    @cached_property
    def pretty_size(self):
        """所有图片大小"""
        return ut.readable_size(self.size)

    @cached_property
    def json_path(self):
        """元数据文件路径"""
        jpath = self.meta_path / 'meta.json'
        jpath.touch()
        return jpath

    @cached_property
    def albums(self):
        """所有专辑"""
        raise NotImplementedError

    def update(self, **kwargs):
        """更新元数据"""
        for k, v in kwargs.items():
            if k in self.__dict__:
                setattr(self, k, v)

    @staticmethod
    def album_parents():
        return [META_DIR / catg.name for catg in Category]

    @staticmethod
    def pick_up_tags():
        """从备选词中筛选标签"""
        print_inf('备选标签:')
        for n, word in enumerate(ALBUM_TAGS):
            fix_word = fill_str(word, 8)
            print(f'{n:2d}. {fix_word}', end='| ')
            if n > 0 and (n + 1) % 6 == 0:
                print()
        tags = input('\n请选择备选标签编号，或直接输入新标签, 用空格分割:\n> ')
        tags = [ALBUM_TAGS[int(t)] if t.isdecimal() else t for t in tags.split()]
        return ','.join(tags)

    def is_complete(self):
        """检查元数据是否完整"""
        if not (self.title and self.catg and self.description and self.s_tags and self.size and self.url):
            return False

        if not (0 <= self.level <= 7):
            return False

        n_sample = len(self.samples)
        if n_sample < 1 or n_sample > 5:
            return False

        return True

    def enter_empty_fields(self):
        """输入空缺字段"""
        print_inf(f'录入专辑: 《 {self.asset_path.name} 》')

        self.title = self.title or input('请输入标题: ').replace(' ', '_')
        self.description = self.description or read_lines('请输入描述信息: ')
        self.s_tags = self.s_tags or self.pick_up_tags()
        self.url = self.url or input('请输入下载链接: ')
        self.extrcode = self.extrcode or input('请输入提取码: ')
        self.password = self.password or input('请输入解压码: ')

        if self.level not in [1, 2, 3, 4, 5, 6, 7]:
            self.level = int(input('请输入等级: '))

    def to_dict(self):
        return {
            'title': self.title.replace('_', ' '),
            'catg': self.catg,
            'level': self.level,
            'description': self.description,
            's_tags': self.s_tags,
            'samples': [path.name for path in self.samples],
            'size': self.size,
            'n_img': self.n_img,
            'url': self.url,
            'password': self.password,
            'extrcode': self.extrcode,
        }

    def dump(self):
        """保存元数据到文件"""
        meta = self.to_dict()
        with open(self.json_path, 'w') as fp:
            json.dump(meta, fp, ensure_ascii=False, separators=(',', ':'), sort_keys=True)

    def converge(self):
        """汇总元数据"""
        # 汇总 JSON 数据
        with open(JSON_COMP, 'r+') as fp:
            try:
                meta_list = json.load(fp)
                all_metas = {meta['title']: meta for meta in meta_list}
            except json.decoder.JSONDecodeError:
                all_metas = {}
            all_metas[self.title] = self.to_dict()  # 汇总到字典中，自动按标题去重
            fp.seek(0)
            fp.truncate()
            meta_list = sorted(all_metas.values(), key=operator.itemgetter('title'))
            json.dump(meta_list, fp, ensure_ascii=False, separators=(',', ':'), sort_keys=True)

        # 汇总样图
        for ipath in self.samples:
            ilink = META_IMAGES / ipath.name
            if not ilink.exists():
                ilink.hardlink_to(ipath)


class AlbumMeta(Meta):
    """专辑元数据"""

    def make_samples(self, overwrite=False):
        """生成样本图片"""
        if self.catg == Category.acg_album:
            for ipath in self.images:
                print_dbg(f'> 生成样本图片: {ipath}')
                name = f'{ipath.stem}_1200.webp'
                src = ACG_LARGE / name
                dst = self.meta_path / name
                if not dst.exists():
                    dst.hardlink_to(src)
        else:
            for ipath in self.images:
                print_dbg(f'> 生成样本图片: {ipath}')
                ut.make_thumbnail(ipath, self.meta_path, 1200, overwrite=overwrite, strict=False)
        print_inf('样本图片生成完毕')

    def enter_empty_fields(self):
        """输入空缺字段"""
        super().enter_empty_fields()

        n_sample = len(self.samples)
        if n_sample == 0:
            self.make_samples()
        if n_sample > 5:
            self.auto_merge()

    def commpress(self, name: str = '', sn: int = 0):
        """压缩专辑"""
        if sn:
            f_name = f'{name}_{sn:03d}' if name else f'{self.title}_{sn:03d}.7z'
        else:
            f_name = name or f'{self.title}.7z'

        zippath = ZIP_DIR / f_name
        print_inf(f'压缩专辑: {zippath}')
        ut.compress_files(zippath, self.asset_path, *ADDITION, password=self.password)
        return zippath

    def preprocess(self, name: str = '', sn: int = 0):
        """专辑预处理"""
        self.commpress(name, sn)
        self.make_samples()

    def auto_merge(self):
        """自动合并样本图片"""
        # 筛选出不以 SAMPLE_PREFIX 开头的图片
        to_process = [
            path
            for path in ut.find_all_images(self.meta_path, recursively=False)
            if not path.stem.startswith(SAMPLE_PREFIX)
        ]
        if not to_process:
            return

        # 按横图、竖图分类
        hor_imgs: list[tuple[Path, Image.Image]] = []  # type: ignore
        ver_imgs: list[tuple[Path, Image.Image]] = []  # type: ignore
        for img_path in to_process:
            img = Image.open(img_path)
            width, height = img.size
            if width > height:
                hor_imgs.append((img_path, img))
            else:
                ver_imgs.append((img_path, img))

        # 使用贪心策略合并图片
        while True:
            n_ver, n_hor = len(ver_imgs), len(hor_imgs)

            if n_hor >= 2 and n_ver >= 1:
                # 有足够的横图和竖图，优先使用 merge_three_images
                v_img, h_img1, h_img2 = ver_imgs.pop(), hor_imgs.pop(), hor_imgs.pop()
                print_dbg(f'> 合并三张图片: {v_img[0].name}, {h_img1[0].name}, {h_img2[0].name}')
                ut.merge_three_images(v_img[1], h_img1[1], h_img2[1], self.meta_path, prefix=SAMPLE_PREFIX)
            elif n_ver >= 2:
                # 有足够的竖图，使用 merge_images
                img1, img2 = ver_imgs.pop(), ver_imgs.pop()
                print_dbg(f'> 合并两张图片: {img1[0].name}, {img2[0].name}')
                ut.merge_images(img1[1], img2[1], self.meta_path, prefix=SAMPLE_PREFIX)
            else:
                break  # 图量不足，退出循环

        for path, _ in ver_imgs:
            print_dbg(f'> 删除样本图片: {path.name}')
            path.unlink()  # 删除不以 prefix 开头的竖图
        for path, img in hor_imgs:
            name = f'{SAMPLE_PREFIX}{ut.str_hash(img.tobytes("jpeg", "RGB"), True)}'
            path.rename(path.with_stem(name))
        print_inf('样本图片删除完毕')


class GalleryMeta(Meta):
    """画集元数据"""

    @cached_property
    def sub_catg(self) -> Category:
        """子专辑分类"""
        if self.catg == Category.acg_gallery:
            return Category.acg_album
        elif self.catg == Category.cos_gallery:
            return Category.cos_album
        else:
            raise ValueError(f'Invalid category: {self.catg}')

    @cached_property
    def albums(self):
        return [AlbumMeta(a_path, self.sub_catg) for a_path in self.asset_path.iterdir()]

    def link_samples(self):
        """将样图链接到合集目录"""
        for a_meta in self.albums:
            for s_path in a_meta.samples:
                s_link = self.meta_path / s_path.name
                if not s_link.exists():
                    s_link.hardlink_to(s_path)

    def preprocess(self, name_prefix: str = ''):
        """预处理画集中的专辑"""
        for n, a_meta in enumerate(self.albums, start=1):
            # TODO：针对超过 1G 或者 200 张图的单个插画专辑自动拆分
            if self.catg == Category.acg_gallery:
                a_meta.preprocess(name_prefix, n)
            else:
                a_meta.preprocess(name_prefix)

    def auto_merge(self):
        """合并画集中的专辑"""
        for a_meta in self.albums:
            a_meta.auto_merge()
        self.link_samples()

    def enter_empty_fields(self):
        """输入空缺字段"""
        for a_meta in self.albums:
            a_meta.enter_empty_fields()
