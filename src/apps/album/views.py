from fastapi import Request

from apps.album import apis as a_api
from common.decorator import page_hits
from libs.http import render


async def album_rcmd(page: int = 1):
    """专辑推荐"""
    albums = await a_api.album_rcmd(page)
    template = 'apps/i_albums.html' if page > 1 else 'apps/albums.html'
    return render(template, albums=albums)


@page_hits('album', 'alb_id')
async def album(request: Request, alb_id: int):
    """专辑详情"""
    album = await a_api.album_detail(alb_id)
    return render('apps/album.html', album=album)


async def purchase_album(request: Request, alb_id: int):
    """使用次元币购买专辑"""
    return await a_api.buy_album(request.user, alb_id)
