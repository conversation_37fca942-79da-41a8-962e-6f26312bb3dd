import config as cfg
from apps.album.models import Album
from apps.user.models import User


async def search_album(kw: str, page: int = 1):
    """搜索专辑"""
    page = max(page, 1)
    offset = (page - 1) * cfg.PER_PAGE

    return await Album.search(kw).limit(cfg.PER_PAGE).offset(offset) if kw else []


async def album_rcmd(page: int = 1):
    """专辑推荐"""
    page = max(page, 1)
    offset = (page - 1) * cfg.PER_PAGE
    albums = await Album.all().order_by('-updated').limit(cfg.PER_PAGE).offset(offset)
    return albums


async def album_detail(alb_id: int):
    """专辑详情"""
    album = await Album.get(id=alb_id)
    album.n_view += 1
    await album.save(update_fields=['n_view'])
    return album


async def buy_album(user: User, alb_id: int):
    """购买专辑"""
    album = await Album.get(id=alb_id)
    if user.vip >= album.free_vip or user.has_album(alb_id):
        payment = await user.buy_album(album, free=True)
        return {'rc': 0, 'msg': 'VIP 用户, 下载免费！', 'new_url': album.url}
    else:
        payment = await user.buy_album(album)
        return {'rc': 0, 'msg': f'次元币 -{payment}', 'new_url': album.url}
