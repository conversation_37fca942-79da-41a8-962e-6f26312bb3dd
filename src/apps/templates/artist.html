{% extends 'base.html' %}

{% block seo %}
    <title>画师 {{ artist.name }} 的作品列表｜次元画册</title>
    <link rel="canonical"
          href="https://www.{{ cfg.DOMAIN }}/w/artist/{{ artist.id }}" />
    <meta name="description"
          content="{{ artist.comment or '画师：' + artist.name + '的作品列表' }}" />
    <meta name="author" content="{{ artist.name }}" />

    {% block social_meta %}
        <meta property="og:type" content="profile" />
        <meta property="og:url"
              content="https://www.{{ cfg.DOMAIN }}/w/artist/{{ artist.id }}" />
        <meta property="og:title"
              content="画师 {{ artist.name }} 的作品列表｜次元画册" />
        <meta property="og:description"
              content="{{ artist.comment or '画师：' + artist.name + '的作品列表' }}" />
        <meta property="og:image" content="{{ artist.avatar }}" />
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url"
              content="https://www.{{ cfg.DOMAIN }}/w/artist/{{ artist.id }}" />
        <meta property="twitter:title"
              content="画师 {{ artist.name }} 的作品列表｜次元画册" />
        <meta property="twitter:description"
              content="{{ artist.comment or '画师：' + artist.name + '的作品列表' }}" />
        <meta property="twitter:image" content="{{ artist.avatar }}" />
    {% endblock social_meta %}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "ImageGallery",
            "name": "画师 {{ artist.name }} 的作品列表｜次元画册",
            "description": "{{ artist.comment or '画师：' + artist.name + '的作品列表' }}",
            "url": "https://www.{{ cfg.DOMAIN }}/w/artist/{{ artist.id }}",
            "image": [
                {% for illust in illusts %}
                    {
                        "@type": "ImageObject",
                        "url": "https://www.{{ cfg.DOMAIN }}/{{ illust.thumbnail }}",
                        "caption": "{{ illust.title }}",
                        "acquireLicensePage": "https://www.{{ cfg.DOMAIN }}/w/privacy/",
                        "license": "https://www.{{ cfg.DOMAIN }}/w/privacy/",
                        "copyrightNotice": "次元画册 - 一切权利归原作者所有, 禁止一切商用行为.",
                        "creditText": "{{ artist.name }}"
                    }{% if not loop.last %},{% endif %}
                {% endfor %}
            ]
        }
    </script>
{% endblock %}

{% block left_side %}
    {% with sz='xxl' %}
        {% include 'i_avatar.html' %}
    {% endwith %}

    <div class="w-100">
        <a class="mgr-4-5"
           href="https://www.pixiv.net/users/{{ artist.id }}"
           target="_blank">
            <i class="fa-brands fa-pixiv fa-lg fg-blue mgr-2-5"></i>
            <strong>Pixiv ID：</strong>{{ artist.id }}
            <i class="fa-solid fa-circle-info"></i>
        </a>

        {% if artist.twitter %}
            <a class="mgr-4-5" href="{{ artist.twitter }}" target="_blank">
                <i class="fa-brands fa-square-x-twitter fa-lg fg-black mgr-2-5"></i>{{ artist.twitter.split("/")[-1] }}
            </a>
        {% endif %}

        {% if artist.webpage %}
            <a class="mgr-4-5" href="{{ artist.webpage }}" target="_blank">
                <i class="fa-solid fa-globe fa-lg fg-cyan mgr-2-5"></i>{{ artist.webpage }}
            </a>
        {% endif %}

        {% if artist.region %}<span><i class="fa-solid fa-location-dot mgr-2-5"></i>{{ artist.region }}</span>{% endif %}
    </div>

    {% if artist.comment %}
        <p class="artist-comment">
            {{ artist.comment | replace('\n', '<br />') | safe }}
        </p>
    {% endif %}

    <hr />

    <h2 class="pure-u-1">画师作品</h2>
    <p class="mgb-1">共收录该画师 {{ artist.n_illust }} 幅作品，部分作品仅对会员可见。</p>
    {% include 'i_illusts.html' %}
{% endblock %}
