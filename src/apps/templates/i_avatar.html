<div class="artist-wrapper w-100">
    <a class="artist-profile" href="/w/artist/{{ artist.id }}">
        <!-- 头像 -->
        <img src="{{ artist.avturl }}"
             alt="画师 {{ artist.name }}"
             class="avatar avatar-{{ artist.gender.value }} mgr-2-5" />
        <!-- 昵称 -->
        <strong class="artist-{{ artist.gender.value }} {{ sz }} ellipsis">{{ artist.name }}</strong>
    </a>

    <!-- 关注按钮 -->
    {% if user and user.is_following(artist.id) %}
        <button id="follow-btn-{{ artist.id }}"
                class="pure-button fg-black bg-gray rd-semi b-500 pdh-1 sm"
                onclick="toggleFollow('{{ artist.id }}')">取关</button>
    {% else %}
        <button id="follow-btn-{{ artist.id }}"
                class="pure-button fg-white bg-blue rd-semi b-500 pdh-1 sm"
                onclick="toggleFollow('{{ artist.id }}')">关注</button>
    {% endif %}
</div>

{% block extra_js %}
    <script type="text/javascript">
        function toggleFollow(artistId) {
            const btn = document.getElementById('follow-btn-' + artistId);
            if (!btn) return;
            const isFollowed = btn.textContent.trim() === '取关';
            const url = isFollowed ? `/a/user/unfollow/${artistId}` : `/a/user/follow/${artistId}`;
            fetchAPI('POST', url).then(res => {
                if (!res) return;
                if (res.rc === 0) {
                    if (isFollowed) {
                        btn.textContent = '关注';
                        btn.classList.remove('fg-black', 'bg-gray');
                        btn.classList.add('fg-white', 'bg-blue');
                    } else {
                        btn.textContent = '取关';
                        btn.classList.remove('fg-white', 'bg-blue');
                        btn.classList.add('fg-black', 'bg-gray');
                    }
                } else {
                    if (window.notyf) notyf.error(res.msg || '操作失败');
                }
            });
        }
    </script>
{% endblock %}
