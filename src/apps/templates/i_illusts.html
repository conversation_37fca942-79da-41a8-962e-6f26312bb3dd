{% if enable_filter %}
    <form id="filterForm" class="pure-form pure-g w-100" method="get">
        <fieldset class="sm">
            <label>排序：</label>
            <select id="order" name="order" class="mgr-1">
                <option value="default" selected>默认</option>
                <option value="created">发布时间</option>
                <option value="n_view">观看量</option>
                <option value="n_bookmark">点赞量 🔥</option>
            </select>

            <label>版式：</label>
            <select id="aspect" name="aspect" class="mgr-1">
                <option value="all" selected>全部</option>
                <option value="ver">竖图</option>
                <option value="hor">横图</option>
                <option value="sqr">方图</option>
            </select>

            <label>𝑺 值：</label>
            <select id="ss" name="ss" class="mgr-1">
                <option value="all" selected>默认</option>
                <option value="2">萌新</option>
                <option value="4">青涩</option>
                <option value="6">绅士</option>
            </select>
        </fieldset>
    </form>
{% endif %}

<div id="illustsContainer"
     class="pure-g w-100"
     data-nsp="{{ sp }}">
    {% if cfg.ENABLE_NATIVE_BANNER_ADULT_AD and ad and not (user and user.has_perms('noad')) %}
        <div class="pure-u-1 pure-u-sm-1-2 pure-u-md-1-3">
            <div class="grid-container sm">
                <div class="thumb-container">{% include "ad_native_banner_adult.html" %}</div>
            </div>
        </div>
    {% endif %}

    {% for illust in illusts %}
        {% if illust.aspect <= 1.1 %}
            <div class="pure-u-1-2 pure-u-sm-1-4 pure-u-md-1-6">
                <div class="grid-container sm">
                    <div class="thumb-container">
                        <!-- 缩略图 -->
                        <div class="reflect">
                            <a href="/w/illust/{{ illust.id }}"
                               title="#{{ illust.main_tag }}: {{ illust.title }}">
                                <img class="thumb-ver"
                                     src="{{ illust.thumbnail }}"
                                     loading="lazy"
                                     alt="#{{ illust.main_tag }} {{ illust.title }} - {{ (illust.artist or artist).name }} 的插画作品" />
                            </a>
                        </div>
                        {% include "i_badge.html" %}
                    </div>
                    {% if cfg.THUMBNAIL_INTRO or show_intro %}
                        <div class="thumb-intro ellipsis">
                            <!-- 插画信息 -->
                            <a href="/w/illust/{{ illust.id }}" class="fg-emp b-700 mg-0">{{ illust.title }}</a>
                            {% if illust.artist %}
                                <a class="artist-link fg-gray"
                                   href="/w/artist/{{ illust.artist.id }}"
                                   title="画师 {{ illust.artist.name }} 的主页">
                                    <img src="{{ illust.artist.avturl }}"
                                         loading="lazy"
                                         class="avatar-mini mgr-2-5"
                                         alt="画师: {{ illust.artist.name }}" />
                                    <span class="ellipsis">{{ illust.artist.name }}</span>
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% else %}
            <div class="pure-u-1 pure-u-sm-1-2 pure-u-md-1-3">
                <div class="grid-container sm">
                    <div class="thumb-container">
                        <!-- 缩略图 -->
                        <div class="reflect">
                            <a href="/w/illust/{{ illust.id }}"
                               title="#{{ illust.main_tag }}: {{ illust.title }}">
                                <img class="thumb-hor"
                                     src="{{ illust.thumbnail }}"
                                     loading="lazy"
                                     alt="#{{ illust.main_tag }} {{ illust.title }} - {{ (illust.artist or artist).name }} 的插画作品" />
                            </a>
                        </div>
                        {% include "i_badge.html" %}
                    </div>
                    {% if cfg.THUMBNAIL_INTRO or show_intro %}
                        <div class="thumb-intro">
                            <!-- 插画信息 -->
                            <a href="/w/illust/{{ illust.id }}"
                               class="fg-emp b-700 mg-0 ellipsis">{{ illust.title }}</a>
                            {% if illust.artist %}
                                <a class="ellipsis artist-link fg-gray"
                                   href="/w/artist/{{ illust.artist.id }}"
                                   title="画师 {{ illust.artist.name }} 的主页">
                                    <img src="{{ illust.artist.avturl }}"
                                         loading="lazy"
                                         class="avatar-mini mgr-2-5"
                                         alt="画师: {{ illust.artist.name }}" />
                                    <span class="ellipsis">{{ illust.artist.name }}</span>
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    {% else %}
        {% include "no_more.html" %}
    {% endfor %}
</div>

{% block extra_js %}
    {% if not disable_dynamic %}
        <script type="text/javascript"
                src="{{ static('js/functions.js') }}"></script>
        <script type="text/javascript">lazyLoadNextPage('illustsContainer');</script>
    {% endif %}

    {% if enable_filter %}
        <script type="text/javascript">
            // 初始化选中状态
            const args = ['order', 'aspect', 'ss'];
            const urlParams = new URLSearchParams(window.location.search);
            for (const arg of args) {
                if (urlParams.has(arg)) {
                    document.getElementById(arg).value = urlParams.get(arg);
                }
            }

            // 监听filterForm变化
            const filterForm = document.getElementById('filterForm');
            if (filterForm) {
                filterForm.addEventListener('change', function(e) {
                    if (e.target.tagName === 'SELECT') {
                        const url = new URL(window.location.href);
                        url.searchParams.set(e.target.name, e.target.value);
                        window.location.href = url.toString();
                    }
                })
            };
        </script>
    {% endif %}
{% endblock %}
