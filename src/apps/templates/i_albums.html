<div id="albumsContainer" class="pure-g w-100">
    {% for alb in albums %}
        <div class="pure-u-1 pure-u-sm-1-2 pure-u-md-1-3 pure-u-lg-1-4">
            <div class="grid-container sm">
                <a href="/w/album/{{ alb.id }}">
                    <div class="thumb-container reflect">
                        <!-- 缩略图 -->
                        <img class="thumb-album" src="{{ alb.thumbnail }}" alt="alb-{{ alb.id }}" />
                    </div>
                    <div class="thumb-intro">
                        <!-- 标题 -->
                        <h4 class="mg-0 ellipsis">
                            <i class="fa-solid fa-circle-info mgr-2-5"></i>{{ alb.title }}
                        </h4>
                        <span class="hor-between">
                            <!-- 专辑标签 -->
                            <strong class="{% if alb.catg == 'Cosplay' %}fg-pink{% else %}fg-blue{% endif %}">
                                <i class="fa-solid fa-tag mgr-2-5"></i>{{ alb.catg }}
                            </strong>
                            <!-- 更新时间 -->
                            <span class="fg-gray"><i class="fa-solid fa-calendar-days mgr-2-5"></i>{{ alb.updated.date() }}</span>
                        </span>
                    </div>
                </a>
            </div>
        </div>
    {% else %}
        {% include "no_more.html" %}
    {% endfor %}
</div>

{% block extra_js %}
    <script type="text/javascript" src="{{ static('js/functions.js') }}"></script>
    <script type="text/javascript">lazyLoadNextPage('albumsContainer');</script>
{% endblock %}
