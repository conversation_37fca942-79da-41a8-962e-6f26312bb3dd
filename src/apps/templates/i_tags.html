<div id="tagsContainer" class="pure-g">
  {% for tag in tags %}
    <div class="pure-u-1-2 pure-u-sm-1-4 pd-2-5">
      <div class="tag-cover">
        <a href="/w/tag/{{ tag.id }}">
          {% if tag.cover %}
            <img src="{{ static('small/', tag.cover) }}" alt="cover" />
          {% else %}
            <img src="{{ static('img/', 'default-cover.webp') }}"
                 alt="cover" />
          {% endif %}
          <span class="ellipsis">
            <i class="fa-solid fa-hashtag mgr-1-5"></i>{{ tag.cname }}
          </span>
        </a>
      </div>
    </div>
  {% else %}
    {% include "no_more.html" %}
  {% endfor %}
</div>

{% block extra_js %}
  <script type="text/javascript"
          src="{{ static('js/functions.js') }}"></script>
  <script type="text/javascript">lazyLoadNextPage('tagsContainer');</script>
{% endblock %}
