{% extends 'base.html' %}

{% block seo %}
    <title>{{ album.title }} - {{ album.ctag }} | 次元画册</title>
    <link rel="canonical"
          href="https://www.{{ cfg.DOMAIN }}/w/album/{{ album.id }}" />
    <meta name="description"
          content="{{ album.description or album.title }}, 作品标签：{{ album.s_tags }}。" />
    <meta name="keywords" content="{{ album.stags }}" />
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "ImageGallery",
            "name": "{{ album.title }} - {{ album.ctag }} | 次元画册",
            "description": "{{ album.description or album.title }}, 作品标签：{{ album.s_tags }}。",
            "url": "https://www.{{ cfg.DOMAIN }}/w/album/",
            "keywords": "{{ album.stags }}"
        }
    </script>
{% endblock %}

{% block left_side %}
    <div class="pure-u-1 pure-u-md-4-5 content">
        <!-- Overview -->
        {% for iurl in album.large_urls %}<img class="pure-u-1" src="{{ iurl }}" alt="album sample" />{% endfor %}

        <!-- AD: Static Banner -->
        {% include "ad_static_banner_normal.html" %}

        <div class="pd-1">
            <a href="#albumTitle">
                <h1 id="albumTitle">{{ album.title }}</h1>
            </a>

            <p class="fg-gray sm">
                <i class="fa-solid fa-calendar-days mgr-2-5"></i>{{ cn_date(album.created, "m") }}
            </p>

            <div class="gbg-blue fg-white pdh-2 rd-15 shadow">
                <p>
                    <strong><i class="fa-solid fa-image w-1 mgr-2-5"></i>图片数量：</strong>{{ album.n_img }} 张
                </p>
                <p>
                    <strong><i class="fa-solid fa-file-zipper w-1 mgr-2-5"></i>资源大小：</strong>{{ album.pretty_size }}
                </p>
                {% if album.description %}
                    <p>
                        <strong><i class="fa-solid fa-circle-info w-1 mgr-2-5"></i>专辑简介：</strong>
                        <p class="mgl-1">{{ album.description | nl2br |safe }}</p>
                    </p>
                {% endif %}
            </div>

            <!-- Tags -->
            <p class="pure-g">
                {% for tag in album.tags %}
                    <a class="tag reflect mg-2-5 bg-{{ loop.cycle('blue', 'dustypurple', 'lightgreen', 'lightblue', 'brown', 'cyan', 'lightpink', 'golden', 'purple') }}"
                       href="/w/search/?stype=album&kw={{ tag }}">
                        <i class="fa-solid fa-hashtag mgr-1-5"></i>{{ tag }}
                    </a>
                {% endfor %}
            </p>

            <!-- 价格、下载 -->
            <hr />
            <div class="hor-around pd-1">
                {% if user and ( user.has_album(album.id) or user.vip >= album.free_vip ) %}
                    <!-- 提取码 -->
                    {% if album.extrcode %}
                        <button class="pure-button vcenter bg-white pd-1 rd-15 bd"
                                onclick="copyExtrCode('{{ album.extrcode }}')">
                            <span class="fg-golden mgr-2-5 pd-1-5"><i class="fa-solid fa-key fa-flip"></i></span>
                            <div class="price-info">
                                复制网盘提取码
                                <strong class="fg-orange">{{ album.extrcode }}</strong>
                            </div>
                        </button>
                    {% endif %}
                    <!-- 下载按钮 -->
                    <a class="pd-1 fg-white bg-dazzling btn-shadow rd-15 vcenter lg b-900 reflect"
                       target="_blank"
                       href="{{ album.url }}">
                        <i class="fa-solid fa-circle-down fa-bounce mgr-2-5"></i>
                        免费下载
                    </a>
                {% else %}
                    <!-- 价格 -->
                    <div class="vcenter pd-1 bd rd-15">
                        <span class="xxl b-900 fg-golden mgr-2-5 pd-1-5">
                            <i class="fa-solid fa-coins"></i>
                        </span>
                        <div class="price-info">
                            <span><strong class="fg-orange">{{ album.price }}</strong> 次元币</span>
                            <span class="fg-rose">
                                <strong>{{ album.free_vip.name[:4] }}免费</strong>
                            </span>
                        </div>
                    </div>
                    <!-- 购买按钮 -->
                    <a class="pd-1 fg-white bg-dazzling rd-15 vcenter lg b-900"
                       href="javascript:void(0);"
                       onclick="purchaseAlbum('{{ album.id }}')">
                        <i class="fa-solid fa-cart-shopping mgr-2-5"></i>立即购买
                    </a>
                {% endif %}
            </div>
            <!-- 密码 -->
            <p class="t-center">
                <i class="fa-solid fa-unlock-keyhole mgr-2-5"></i>
                本资源包的解压密码为：<strong class="fg-red">{{ album.password }}</strong>
            </p>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script type="text/javascript">
        function copyExtrCode(extrcode) {
            // 尝试使用现代剪贴板API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(extrcode).then(function() {
                    notyf.success('提取码已复制到剪贴板');
                }).catch(function(err) {
                    // 如果API调用失败，回退到备选方法
                    fallbackCopyTextToClipboard(extrcode);
                });
            } else {
                // 浏览器不支持clipboard API，使用备选方法
                fallbackCopyTextToClipboard(extrcode);
            }
        };

        // 备选的复制方法，使用临时DOM元素
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            // 设置样式使元素不可见
            Object.assign(textArea.style, {
                position: "fixed",
                top: "0",
                left: "0",
                width: "2em",
                height: "2em",
                padding: "0",
                border: "none",
                outline: "none",
                background: "transparent"
            });

            document.body.appendChild(textArea);

            // iOS设备特殊处理
            if (navigator.userAgent.match(/ipad|iphone/i)) {
                textArea.contentEditable = true;
                textArea.readOnly = false;
                var range = document.createRange();
                range.selectNodeContents(textArea);
                var selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
                textArea.setSelectionRange(0, 999999);
            } else {
                textArea.select();
            }

            try {
                var successful = document.execCommand('copy');
                notyf.success(successful ? '提取码已复制到剪贴板' : '请长按提取码并手动复制');
            } catch (err) {
                notyf.warning('请长按提取码并手动复制');
            }

            document.body.removeChild(textArea);
        };

        // 购买专辑
        function purchaseAlbum(alb_id) {
            fetchAPI('GET', `/w/album/purchase/${alb_id}`).then(function (res) {
                // rc == 0 时刷新当前页面
                if (res.rc === 0) {
                    window.location.reload();
                }
            })
            ;
        }
    </script>
{% endblock %}
