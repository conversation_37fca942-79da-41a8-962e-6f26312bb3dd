import datetime
from typing import ClassVar

from tortoise import fields
from tortoise.transactions import in_transaction

import common.errors as err
from apps.album.models import Album
from apps.pixiv.models import <PERSON>, IAligner, Illust
from common.notice import add_notice
from common.utils import safe_password
from config import PER_PAGE, PERMISSIONS, TIME_ZONE, Vip
from libs.orm import Model

__all__ = ['User']


class User(Model):
    """用户"""

    email = fields.CharField(max_length=32, unique=True, description='邮箱')
    password = fields.CharField(max_length=128, description='密码')
    name = fields.CharField(max_length=64, description='昵称')
    coins = fields.IntField(unsigned=True, default=100, description='次元币')
    created = fields.DatetimeField(auto_now_add=True, db_index=True, description='注册时间')
    banned = fields.BooleanField(default=False, description='账号是否被封')
    purchased: dict[str, list[int]] = fields.JSONField(default={'illusts': [], 'albums': []}, description='已购内容')  # type: ignore
    followed: list[int] = fields.JSONField(default=[], description='关注的画师列表')  # type: ignore
    favorited: list[int] = fields.JSONField(default=[], description='收藏的插画列表')  # type: ignore
    # VIP 相关
    vid = fields.SmallIntField(unsigned=True, default=0, description='VIP ID')
    vstart = fields.DatetimeField(default=None, null=True, description='VIP 开始时间')
    vend = fields.DatetimeField(default=None, null=True, description='VIP 结束时间')

    class Meta:  # type: ignore
        table = 'users'
        ordering: ClassVar[list[str]] = ['id']

    @property
    def vip(self):
        """VIP"""
        if self.vend and self.vend > datetime.datetime.now(TIME_ZONE):
            return Vip.get(self.vid)
        return Vip.get(0)

    async def ban(self):
        """封号"""
        self.banned = True
        await self.save(update_fields=['banned'])

    async def unban(self):
        """解封"""
        self.banned = False
        await self.save(update_fields=['banned'])

    async def set_password(self, password):
        """设置密码"""
        self.password = safe_password(password)
        await self.save(update_fields=['password'])

    def verify_password(self, password):
        """验证密码"""
        return safe_password(password) == self.password

    async def purchased_illusts(self, page: int = 1, per_page: int = PER_PAGE, order: str = 'purchased', spos: int = 0):
        """已购买的插画"""
        offset = per_page * (page - 1)
        if order == 'purchased':
            start, end = -(offset + 1), -(offset + 1 + per_page)
            i_ids = self.purchased['illusts'][start:end:-1]
            illusts = await Illust.filter(id__in=i_ids)
            illusts.sort(key=lambda i: i_ids.index(i.id))
        else:
            illusts = (
                await Illust.filter(id__in=self.purchased['illusts'])
                .order_by(f'-{order}')
                .limit(per_page)
                .offset(offset)
            )

        return IAligner(spos).align(illusts)

    async def purchased_albums(self, page: int = 1, per_page: int = PER_PAGE):
        """已购买的专辑"""
        offset = per_page * (page - 1)
        start, end = -(offset + 1), -(offset + 1 + per_page)
        a_ids = self.purchased['albums'][start:end:-1]
        albums = await Album.filter(id__in=a_ids)
        albums.sort(key=lambda a: a_ids.index(a.id))
        return albums

    async def buy_illust(self, illust: int | Illust, free=False):
        """购买插画"""
        async with in_transaction():
            if isinstance(illust, int):
                illust = await Illust.get(id=illust)

            if illust.id in self.purchased['illusts']:
                return 0  # 重复购买，不扣钱

            if not free:
                await self.spend(illust.price, False)  # 扣除次元币
            self.purchased['illusts'].append(illust.id)  # 添加到已购列表
            await self.save(update_fields=['purchased', 'coins'])

            # 下载次数 +1
            illust.n_download += 1
            await illust.save(update_fields=['n_download'])

            return 0 if free else illust.price

    async def buy_album(self, album: int | Album, free=False):
        """购买专辑"""
        async with in_transaction():
            if isinstance(album, int):
                album = await Album.get(id=album)

            if album.id in self.purchased['albums']:
                return 0  # 重复购买，不扣钱

            if not free:
                await self.spend(album.price, False)  # 扣除次元币
            self.purchased['albums'].append(album.id)  # 添加到已购列表
            await self.save(update_fields=['purchased', 'coins'])

            # 下载次数 +1
            album.n_download += 1
            await album.save(update_fields=['n_download'])

            return 0 if free else album.price

    async def follow(self, aid: int):
        """关注画师"""
        if aid not in self.followed:
            self.followed.append(aid)
            await self.save(update_fields=['followed'])
            return True
        return False

    async def unfollow(self, aid: int):
        """取消关注"""
        if aid in self.followed:
            self.followed.remove(aid)
            await self.save(update_fields=['followed'])
            return True
        return False

    def is_following(self, aid: int) -> bool:
        """是否关注"""
        return aid in self.followed

    async def followed_artists(self, page: int = 1, per_page: int = PER_PAGE):
        """关注的画师"""
        start = -(per_page * (page - 1)) - 1
        end = -(per_page * page) - 1
        aids = self.followed[start:end:-1]
        if not aids:
            return []
        artists = await Artist.filter(id__in=aids)
        artists.sort(key=lambda a: aids.index(a.id))
        return artists

    async def favorite(self, iid: int):
        """收藏插画"""
        if iid not in self.favorited:
            self.favorited.append(iid)
            await self.save(update_fields=['favorited'])
            return True
        return False

    async def unfavorite(self, iid: int):
        """取消收藏插画"""
        if iid in self.favorited:
            self.favorited.remove(iid)
            await self.save(update_fields=['favorited'])
            return True
        return False

    def is_favorited(self, iid: int) -> bool:
        """检查是否收藏插画"""
        return iid in self.favorited

    async def favorited_illusts(self, page: int = 1, per_page: int = PER_PAGE):
        """获取收藏的插画"""
        start = -(per_page * (page - 1)) - 1
        end = -(per_page * page) - 1
        i_ids = self.favorited[start:end:-1]
        illusts = await Illust.filter(id__in=i_ids)
        illusts.sort(key=lambda i: i_ids.index(i.id))
        return IAligner().align(illusts)

    async def set_vip(self, vid: int, save: bool = True):
        """购买 VIP"""
        new_vip = Vip.get(vid)
        now = datetime.datetime.now(TIME_ZONE)
        upgrade = vid > self.vid
        if vid <= 0 or new_vip.price <= 0:
            self.vid = 0
            self.vstart = self.vend = now
        else:
            if self.vip.id > 0:
                remaining_days = (self.vend - now).days
                remaining_price = remaining_days / self.vip.duration * self.vip.price
            else:
                remaining_price = 0
            duration = (remaining_price / new_vip.price + 1) * new_vip.duration
            self.vid = new_vip.id
            self.vstart = now
            self.vend = now + datetime.timedelta(days=duration)

        if save:
            await self.save(update_fields=['vid', 'vstart', 'vend'])
            if upgrade:
                length = len(self.name)
                if length >= 2:
                    l1, l2 = length // 2, length - length // 2
                    name = self.name[:l1] + '*' * l2
                else:
                    name = f'{self.name}*'
                await add_notice(f'🎉🎉🎉 恭喜 {name} 成为【{new_vip.name}】', 86400 * 7)

    async def spend(self, amount: int, save: bool = True):
        """花费次元币"""
        if amount < 0:
            raise err.CoinError
        if self.coins < amount:
            raise err.NotSufficientCoins

        self.coins -= amount

        if save:
            await self.save(update_fields=['coins'])

    async def topup(self, amount: int, save: bool = True):
        """充值次元币"""
        if amount < 0:
            raise err.CoinError

        self.coins += amount

        if save:
            await self.save(update_fields=['coins'])

    def has_perms(self, *perms: str) -> bool:
        """检查权限"""
        for perm in perms:
            perm_cfg = PERMISSIONS.get(perm)
            if perm_cfg and self.vip.level >= perm_cfg[0]:
                continue
            else:
                return False
        return True

    def has_illust(self, iid: int) -> bool:
        """检查是否拥有插画"""
        return iid in self.purchased['illusts']

    def has_album(self, aid: int) -> bool:
        """检查是否拥有专辑"""
        return aid in self.purchased['albums']
