"""程序逻辑配置"""

from collections import OrderedDict

PER_PAGE = 36  # 分页时，每页内容数量
LEN_VCODE = 6  # 验证码长度
PWD_MIN_LEN = 6  # 密码最小长度
THUMBNAIL_INTRO = True  # 是否在缩略图底部显示插画标题及作者
ENABLE_DOWNLOAD_1K = False  # 是否允许下载 1K 分辨率的图片

# 不同用户允许的最大敏感等级
MAX_SANITY = {
    'anonymous': 2,
    'registered': 4,
    'low-vip': 5,
    'vip': 7,
    'super-vip': 7,
}

# 每日打卡奖励
CHECKIN_AWARD = OrderedDict({
    '第 1 次': 10,
    '第 2 次': 20,
    '第 3 次': 10,
    '第 4 次': 10,
    '第 5 次': 10,
    '第 6 次': 20,
    '第 7 次': 70,
})

# AD Switch
ENABLE_POPUNDER_ADULT_AD = False  # 弹窗广告开关
ENABLE_STATIC_BANNER_NORMAL_AD = True  # 静态 Banner 广告开关
ENABLE_STATIC_BANNER_ADULT_AD = True  # 静态 Banner 广告开关
ENABLE_NATIVE_BANNER_ADULT_AD = True  # Native Banner 广告开关
