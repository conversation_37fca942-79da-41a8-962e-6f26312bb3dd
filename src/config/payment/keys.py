from base64 import decodebytes, encodebytes
from pathlib import Path
from pickle import HIGHEST_PROTOCOL, dumps, loads  # noqa: S403

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric.rsa import RSAPrivate<PERSON><PERSON>, RSAPublicKey

__all__ = ['MD5_KEY', 'PRIVATE_KEY', 'PUBLIC_KEY']

MD5_KEY = b'ypqCp4pjqNMYiHJ1K2Mc0803iA3IIPn2'
KEYS_DIR = Path(__file__).parent / Path(__file__).stem
PUBLIC_KEY_PATH = KEYS_DIR / 'secret.pub'  # 平台公钥
PRIVATE_KEY_PATH = KEYS_DIR / 'secret.key'  # 商户私钥


def encode(content: bytes | str):
    '''将 content 进行 base64 编码'''
    if isinstance(content, str):
        content = content.encode('utf-8')
    return encodebytes(dumps(content, HIGHEST_PROTOCOL))


def decode(content: bytes) -> bytes:
    '''将 content 进行 base64 解码'''
    content = loads(decodebytes(content))  # noqa: S301
    if isinstance(content, str):
        content = content.encode('utf-8')
    return content


def read_key(path: str | Path):
    '''读取密钥'''
    with Path(path).open('rb') as fp:
        content = fp.read()
        return decode(content)


def write_key(path: str | Path, content: bytes | str):
    '''写入密钥'''
    with Path(path).open('wb') as fp:
        fp.write(encode(content))


# 商户私钥
PRIVATE_KEY: RSAPrivateKey = serialization.load_pem_private_key(read_key(PRIVATE_KEY_PATH), password=None)  # type: ignore

# 平台公钥
PUBLIC_KEY: RSAPublicKey = serialization.load_pem_public_key(read_key(PUBLIC_KEY_PATH))  # type: ignore
