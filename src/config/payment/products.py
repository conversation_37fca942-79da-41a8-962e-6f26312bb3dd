import json
from collections import OrderedDict
from pathlib import Path
from typing import Self

JSON_DIR = Path(__file__).parent / Path(__file__).stem

__all__ = ['PERMISSIONS', 'Coin', 'Vip']


# 会员权限
PERMISSIONS = OrderedDict({
    'dbl_chkin': (1, '打卡奖励翻倍'),
    'fl_order': (1, '按点赞数排序'),
    'fl_aspect': (1, '按宽高比过滤'),
    'fl_sanity': (1, '按 𝑺 值过滤'),
    'dl_illust': (1, '免费下载二次元美图'),
    'noad': (2, '没有广告'),
    'dl_acg_album': (3, '免费下载二次元专辑'),
    'dl_cos_album': (3, '免费下载 Cosplay 专辑'),
    'dl_acg_gallery': (4, '免费下载二次元合集'),
    'dl_cos_gallery': (4, '免费下载 Coser 合集'),
    'dl_acg_base': (5, '免费下载二次元图库'),
    'dl_cos_base': (5, '免费下载 Cosplay 图库'),
})


class JsonConfig:
    """Base class for JSON configs"""

    id: int

    def __init__(self, json_data: dict):
        for field_name, field_type in self.__annotations__.items():
            field_value = json_data.get(field_name)
            if isinstance(field_value, field_type):
                setattr(self, field_name, field_value)
            else:
                raise TypeError(
                    f'Invalid type for {field_name}={field_value}. Expected {field_type}, got {field_type(field_value)}'
                )

    def __lt__(self, other: Self) -> bool:
        return self.id < other.id

    def __str__(self) -> str:
        return f'{self.__class__.__name__}({self.id})'

    def __repr__(self) -> str:
        return f'{self.__class__.__name__}({self.id})'

    @classmethod
    def loads(cls, json_name: str) -> list[Self]:
        json_path = JSON_DIR / json_name
        with open(json_path) as f:
            data = json.load(f)

        objs = [cls(obj) for obj in data]
        objs.sort()
        return objs

    @classmethod
    def get(cls, id: int) -> Self:  # noqa: A002
        return cls.all()[id]

    @classmethod
    def all(cls) -> list[Self]:
        if not hasattr(cls, '_all'):
            name = cls.__name__.lower()
            cls._all = cls.loads(f'{name}s.json')  # type: ignore
        return cls._all  # type: ignore


class Vip(JsonConfig):
    id: int
    name: str
    description: str
    level: int
    original: int | float
    price: int | float
    duration: int
    hidden: bool

    def __str__(self):
        icons = '　🍭🥤🍔💎👑'
        icon = icons[self.level]
        return f'{icon} Vip-{self.level}'

    def __eq__(self, other: object) -> bool:
        if isinstance(other, Vip):
            return self.level == other.level
        return False

    def __lt__(self, other) -> bool:
        if isinstance(other, Vip):
            return self.level < other.level
        return False

    def __le__(self, other: Self) -> bool:
        if isinstance(other, Vip):
            return self.level <= other.level
        return False

    def __gt__(self, other: Self) -> bool:
        if isinstance(other, Vip):
            return self.level > other.level
        return False

    def __ge__(self, other: Self) -> bool:
        if isinstance(other, Vip):
            return self.level >= other.level
        return False

    @classmethod
    def need_vip(cls, *perms):
        max_vip_level = max(PERMISSIONS[perm][0] for perm in perms)
        for vip in sorted(cls.all(), key=lambda v: (v.level, v.id)):
            if vip.level >= max_vip_level:
                return vip
        raise ValueError(f'No vip level >= {max_vip_level}')


class Coin(JsonConfig):
    id: int
    name: str
    description: str
    amount: int
    price: int | float
