"""FastAPI Configs"""

import os
from zoneinfo import ZoneInfo

APP_NAME = '次元画册'
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ALBUM_ENV = os.environ.setdefault('ALBUM_ENV', 'test').lower()  # 环境变量: prod, remote, test
DEBUG = False if ALBUM_ENV != 'test' else True
DOMAIN = 'pixcc.net'
FAV_DOMAIN = 'cyhc.cc'
ALLOWED_HOSTS = ['*']  # 允许的主机列表
TRUSTED_HOSTS = ['*']  # 信任的主机列表
ADMIN_EMAIL = '<EMAIL>'

# 设置全局时区
os.environ['TZ'] = 'Asia/Shanghai'
TIME_ZONE = ZoneInfo(os.environ['TZ'])
CACHE_MAX_AGE = 86400 * 3  # 缓存有效期 3 天
SESSION_MAX_AGE = 86400 * 365 * 10  # 会话有效期 10 年
SECRET_KEY = b'\x84\x8e\xab\xae \x9aQ\xa9\xd9\x95\x81\xd3,I\xa0\xcf'

# 静态文件配置
STORAGE = 'cloudflare'  # 静态文件存储方式: local, b2, cloudflare
STATIC_DIR = os.path.join(BASE_DIR, 'static')
STATIC_URL = '/static'
# B2 存储配置
B2 = {
    'key_name': 'pixcc',
    'key_id': '00423f97f161a290000000009',
    'app_key': 'K004riuBFFObST5S2SPHwR3WA/JIRek',
    'root': 'https://f004.backblazeb2.com/file',
    'public': 'ipixiv',
    'private': 'ipixcc',
}
# CloudFlare CDN 配置
CF_ROOT = 'https://cdn.pixcc.net'

# CloudFlare Turnstile
CF_TUR_SITE_KEY = '0x4AAAAAABC5mM_HIWHAka0h'
CF_TUR_SECRET_KEY = '0x4AAAAAABC5mNLAmKN5PH9azzBI8-4afBA'  # noqa: S105

# Jinja2 Configs
TEMPLATES = {
    'admin': os.path.join(BASE_DIR, 'admin/templates'),
    'apps': os.path.join(BASE_DIR, 'apps/templates'),
}

# Uvicorn Configs
UVICORN = {
    'reload': DEBUG,
    'workers': 1 if DEBUG else (os.cpu_count() or 1) * 2,
    'access_log': DEBUG,  # 访问日志开关
    'use_colors': True,
    'backlog': 2048,
    'limit_concurrency': 10000,
    'limit_max_requests': 65535,
    'timeout_keep_alive': 65,  # 长连接超时时间
    'timeout_graceful_shutdown': 30.0,  # 强制关闭非空闲连接超时时间
}

# Redis Config
REDIS = {
    'host': 'localhost',
    'port': 6379,
    'db': ['prod', 'test', 'remote'].index(ALBUM_ENV) + 1,  # Redis DB 选择
}

# Database Config
DB_CONNS = {
    'prod': 'postgres://seamile:CeHai&758521@localhost:5432/albumdb',
    'test': 'postgres://seamile:7758521@localhost:5432/albumdb_test',
    'remote': 'postgres://seamile:CeHai&758521@localhost:2345/albumdb',
}
DATABASE = {
    'connections': {'default': DB_CONNS[ALBUM_ENV]},
    'apps': {
        APP_NAME: {
            'models': ['apps.pixiv.models', 'apps.user.models', 'apps.payment.models'],
            'default_connection': 'default',
        }
    },
}


# Log Configs
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'simple': {
            'format': '%(asctime)s %(module)s.%(funcName)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'verbose': {
            'format': ('%(asctime)s %(levelname)s %(module)s.%(funcName)s (%(lineno)d): %(message)s'),
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'info': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': f'{BASE_DIR}/logs/info.log',
            'when': 'midnight',
            'backupCount': 30,
            'formatter': 'simple',
        },
        'error': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': f'{BASE_DIR}/logs/error.log',
            'when': 'W0',
            'backupCount': 4,
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'info': {
            'handlers': ['console' if DEBUG else 'info'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': True,
        },
        'error': {
            'handlers': ['console' if DEBUG else 'error'],
            'level': 'DEBUG' if DEBUG else 'WARNING',
            'propagate': True,
        },
    },
}

# EMAIL Config
EMAIL = {
    'API_KEY': '1ead088377147532d31c2cca4d835ab0',  # API KEY / SMTP登录名
    'API_SECRET': '8577ed10f69c5049c09ab346b44e9ed1',  # API SECRET / SMTP登录密码
    'HOST': 'in-v3.mailjet.com',  # SMTP服务器地址
    'PORT': 465,  # SMTP服务器端口
    'SENDER_NAME': APP_NAME,  # 发件人名称
    'SENDER_MAIL': f'service@{DOMAIN}',  # 发件人邮箱
}

# Short URL Configs
SHORT_URL = {
    'api': 'https://www.urlc.cn/api/url/add',
    'key': '853oPA5ura9XKjzP7mqO',
}
