limit_conn_zone $binary_remote_addr zone=connlimit:10m;
limit_req_zone $binary_remote_addr zone=darkroom:10m rate=5r/s;

server {
    listen 80;
    server_name pixcc.net www.pixcc.net;
    return 301 https://www.pixcc.net$request_uri;
}

server {
    listen 443 ssl;
    http2 on;
    server_name pixcc.net;

    ssl_certificate     /etc/nginx/ssl/pixcc.crt;
    ssl_certificate_key /etc/nginx/ssl/pixcc.key;

    return 301 https://www.pixcc.net$request_uri;
}

server {
    listen 443 ssl;
    http2 on;
    server_name www.pixcc.net;

    # SSL/TLS
    ssl_certificate     /etc/nginx/ssl/pixcc.crt;
    ssl_certificate_key /etc/nginx/ssl/pixcc.key;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256';

    # Enable SSL session caching
    ssl_session_cache   shared:SSL:10m;
    ssl_session_timeout 1h;
    ssl_session_tickets on;
    ssl_dhparam         /etc/ssl/certs/dhparam.pem;
    ssl_ecdh_curve      secp384r1;

    # 开启 OCSP Stapling 以提高 TLS 握手性能
    ssl_stapling on;
    ssl_stapling_verify on;

    # Improve DNS resolution
    resolver            ******* ******* valid=300s;
    resolver_timeout    5s;

    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    location = /favicon.ico {
        alias           /opt/albumd/static/img/favicon.ico;
        access_log      off;
        expires         30d;
    }

    location = /robots.txt {
        alias           /opt/albumd/config/site/robots.txt;
        access_log      off;
        expires         30d;
    }

    location /static/ {
        root            /opt/albumd;
        access_log      off;
        expires         7d;
        add_header      Cache-Control "public";
    }

    location ~ ^/(sitemap.*\.xml)$ {
        alias           /opt/albumd/sitemap/$1;
        access_log      off;
        expires         7d;
    }

    location ~ ^/(w/|a/)? {
        # 使用真实 IP 进行限制
        limit_conn connlimit 10;
        limit_conn_log_level warn;

        limit_req zone=darkroom burst=10 nodelay;
        limit_req_log_level warn;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_redirect   off;
        proxy_buffering  off;
        proxy_pass       http://uvicorn;
    }

    # 默认拒绝所有其他未明确匹配的请求
    location / {
        return 444;
    }
}

# Map for WebSocket
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

# Upstream
upstream uvicorn {
    server unix:/tmp/albumd.sock;
}
