# 阻止直接通过 IP 地址访问的请求
server {
    listen 80 default_server;
    listen 443 ssl default_server;

    # 通过自签名的证书来处理 SSL 握手
    # 可以使用以下命令生成:
    # openssl req -x509 -nodes -days 3650 -newkey rsa:2048 -keyout /etc/nginx/ssl/dummy.key -out /etc/nginx/ssl/dummy.crt -subj "/CN=localhost"
    ssl_certificate /etc/nginx/ssl/dummy.crt;
    ssl_certificate_key /etc/nginx/ssl/dummy.key;

    # 捕获所有未匹配的 Host 头部
    server_name _;
    return 444;
}
